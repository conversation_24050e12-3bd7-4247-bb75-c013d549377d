[gd_scene load_steps=5 format=3 uid="uid://bwamxutjv78ov"]

[ext_resource type="Texture2D" uid="uid://btpptirbgtfof" path="res://Other/Demo1/circuit_background.png" id="1_0bhed"]
[ext_resource type="Texture2D" uid="uid://hsjwglr34nf1" path="res://Other/Demo1/mouse_01.png" id="1_5hfv0"]
[ext_resource type="Script" uid="uid://b32hli1aounq0" path="res://Other/Demo1/player.gd" id="2_m0rpm"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_0bhed"]
radius = 31.0
height = 98.0

[node name="Demo" type="Node2D"]

[node name="Background" type="Sprite2D" parent="."]
texture = ExtResource("1_0bhed")

[node name="Player" type="CharacterBody2D" parent="."]
position = Vector2(576, 324)
script = ExtResource("2_m0rpm")

[node name="Sprite2D" type="Sprite2D" parent="Player"]
texture = ExtResource("1_5hfv0")

[node name="CollisionShape2D" type="CollisionShape2D" parent="Player"]
rotation = 1.5708
shape = SubResource("CapsuleShape2D_0bhed")

[node name="Camera2D" type="Camera2D" parent="Player"]
