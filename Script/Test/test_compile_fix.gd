extends Node

## 测试编译修复的脚本

func _ready():
	print("=== 编译修复测试 ===")
	test_basic_functionality()

func test_basic_functionality():
	print("1. 测试基本功能...")
	
	# 测试ItemDatabase
	print("  - 测试ItemDatabase...")
	var template = ItemDatabase.get_item("potion_health")
	if template:
		print("    ✓ ItemDatabase.get_item() 工作正常")
	else:
		print("    ✗ ItemDatabase.get_item() 失败")
	
	# 测试TagUtil
	print("  - 测试TagUtil...")
	if template:
		var usable = TagUtil.is_usable(template)
		var equipable = TagUtil.is_equipable(template)
		print("    ✓ TagUtil.is_usable() 和 is_equipable() 工作正常")
		print("      可使用: %s, 可装备: %s" % [usable, equipable])
	
	# 测试InventoryManager
	print("  - 测试InventoryManager...")
	InventoryManager.clear_inventory()
	var success = InventoryManager.add_item("potion_health", 1)
	if success:
		print("    ✓ InventoryManager.add_item() 工作正常")
	else:
		print("    ✗ InventoryManager.add_item() 失败")
	
	# 测试InventorySlot
	print("  - 测试InventorySlot...")
	var slot = InventoryManager.inventory_slots[0]
	if not slot.is_empty():
		var slot_template = slot.get_template()
		if slot_template:
			print("    ✓ InventorySlot.get_template() 工作正常")
			print("      槽位物品: %s" % slot_template.name)
		else:
			print("    ✗ InventorySlot.get_template() 返回null")
	
	print("=== 测试完成 ===")
