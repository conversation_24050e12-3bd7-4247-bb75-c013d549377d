extends Node

## 物品系统重构测试脚本
## 验证新架构的正确性

func _ready():
	print("开始物品系统重构测试...")
	run_all_tests()

func run_all_tests():
	test_template_immutability()
	test_instance_independence()
	test_inventory_operations()
	test_tag_util_compatibility()
	print("\n所有测试完成！")

## 测试1：模板不可变性
func test_template_immutability():
	print("\n=== 测试1：模板不可变性 ===")
	
	# 获取模板
	var template = ItemDatabase.get_item("potion_health")
	if not template:
		print("❌ 无法获取potion_health模板")
		return
	
	var original_use = TagUtil.tagi(template, "use", 0)
	print("✓ 模板原始use值: %d" % original_use)
	
	# 创建实例并使用
	var instance = ItemDatabase.create_item_instance("potion_health")
	print("✓ 实例初始use值: %d" % instance.get_remaining_uses())
	
	# 消耗实例
	instance.consume_use()
	print("✓ 实例消耗后use值: %d" % instance.get_remaining_uses())
	
	# 验证模板未被修改
	var template_use_after = TagUtil.tagi(template, "use", 0)
	if template_use_after == original_use:
		print("✅ 模板保持不变: %d" % template_use_after)
	else:
		print("❌ 模板被意外修改: %d -> %d" % [original_use, template_use_after])

## 测试2：实例独立性
func test_instance_independence():
	print("\n=== 测试2：实例独立性 ===")
	
	# 创建两个相同的实例
	var instance1 = ItemDatabase.create_item_instance("potion_health")
	var instance2 = ItemDatabase.create_item_instance("potion_health")
	
	print("✓ 实例1初始状态: %d uses" % instance1.get_remaining_uses())
	print("✓ 实例2初始状态: %d uses" % instance2.get_remaining_uses())
	
	# 只使用实例1
	instance1.consume_use()
	
	print("✓ 实例1使用后: %d uses" % instance1.get_remaining_uses())
	print("✓ 实例2状态: %d uses" % instance2.get_remaining_uses())
	
	# 验证实例独立性
	if instance1.get_remaining_uses() != instance2.get_remaining_uses():
		print("✅ 实例状态独立")
	else:
		print("❌ 实例状态相互影响")

## 测试3：背包操作
func test_inventory_operations():
	print("\n=== 测试3：背包操作 ===")
	
	# 清空背包
	InventoryManager.clear_inventory()
	print("✓ 背包已清空")
	
	# 添加物品
	var success = InventoryManager.add_item("potion_health", 3)
	if success:
		print("✅ 成功添加3个生命药水")
	else:
		print("❌ 添加物品失败")
		return
	
	# 检查数量
	var quantity = InventoryManager.get_item_quantity("potion_health")
	if quantity == 3:
		print("✅ 背包中有%d个生命药水" % quantity)
	else:
		print("❌ 数量不正确: 期望3，实际%d" % quantity)
	
	# 使用一个物品
	var use_success = InventoryManager.use_item(0)
	if use_success:
		print("✅ 成功使用物品")
	else:
		print("❌ 使用物品失败")
	
	# 检查使用后的状态
	var slot = InventoryManager.inventory_slots[0]
	if not slot.is_empty():
		print("✓ 槽位仍有物品: %d个" % slot.get_quantity())
		
		# 检查实例状态
		for i in range(slot.item_instances.size()):
			var instance = slot.item_instances[i]
			print("  实例%d剩余使用次数: %d" % [i, instance.get_remaining_uses()])
	else:
		print("❌ 槽位意外为空")

## 测试4：TagUtil兼容性
func test_tag_util_compatibility():
	print("\n=== 测试4：TagUtil兼容性 ===")
	
	# 测试模板访问
	var template = ItemDatabase.get_item("sword_iron")
	if template:
		var attack = TagUtil.tagi(template, "attack", 0)
		var slot = TagUtil.tags(template, "slot", "")
		print("✓ 模板访问 - 攻击力: %d, 槽位: %s" % [attack, slot])
	
	# 测试实例访问
	var instance = ItemDatabase.create_item_instance("sword_iron")
	if instance:
		var attack = TagUtil.tagi(instance, "attack", 0)
		var slot = TagUtil.tags(instance, "slot", "")
		var usable = TagUtil.is_usable(instance)
		var equipable = TagUtil.is_equipable(instance)
		print("✓ 实例访问 - 攻击力: %d, 槽位: %s" % [attack, slot])
		print("✓ 实例状态 - 可使用: %s, 可装备: %s" % [usable, equipable])
	
	print("✅ TagUtil兼容性测试通过")

## 额外测试：打印详细状态
func print_detailed_status():
	print("\n=== 详细状态 ===")
	InventoryManager.print_inventory()
	InventoryManager.debug_print_tags()
