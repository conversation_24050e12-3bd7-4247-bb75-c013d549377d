extends Node

## 物品系统重构演示
## 展示新架构如何正确分离物品模板和实例状态

func _ready():
	print("=== 物品系统重构演示 ===")
	demo_template_vs_instance()
	demo_tag_immutability()
	demo_inventory_operations()

## 演示模板与实例的分离
func demo_template_vs_instance():
	print("\n1. 模板与实例分离演示:")
	
	# 获取物品模板（不可变）
	var potion_template = ItemDatabase.get_item("potion_health")
	if not potion_template:
		print("Error: potion_health template not found")
		return
	
	print("模板信息:")
	print("  ID: %s" % potion_template.id)
	print("  名称: %s" % potion_template.name)
	print("  模板tags: %s" % potion_template.tags)
	print("  模板use值: %d" % TagUtil.tagi(potion_template, "use", 0))
	
	# 创建多个实例
	var instance1 = ItemDatabase.create_item_instance("potion_health")
	var instance2 = ItemDatabase.create_item_instance("potion_health")
	
	print("\n实例1信息:")
	print("  模板ID: %s" % instance1.template.id)
	print("  剩余使用次数: %d" % instance1.get_remaining_uses())
	print("  可使用: %s" % instance1.is_usable())
	
	print("\n实例2信息:")
	print("  模板ID: %s" % instance2.template.id)
	print("  剩余使用次数: %d" % instance2.get_remaining_uses())
	print("  可使用: %s" % instance2.is_usable())
	
	# 使用实例1
	print("\n使用实例1...")
	instance1.consume_use()
	
	print("使用后状态:")
	print("  实例1剩余使用次数: %d" % instance1.get_remaining_uses())
	print("  实例1可使用: %s" % instance1.is_usable())
	print("  实例2剩余使用次数: %d" % instance2.get_remaining_uses())
	print("  实例2可使用: %s" % instance2.is_usable())
	print("  模板use值(不变): %d" % TagUtil.tagi(potion_template, "use", 0))

## 演示标签的不可变性
func demo_tag_immutability():
	print("\n2. 标签不可变性演示:")
	
	var sword_template = ItemDatabase.get_item("sword_iron")
	if not sword_template:
		print("Error: sword_iron template not found")
		return
	
	print("剑模板原始属性:")
	print("  攻击力: %d" % TagUtil.tagi(sword_template, "attack", 0))
	print("  槽位: %s" % TagUtil.tags(sword_template, "slot", ""))
	
	# 创建实例
	var sword_instance = ItemDatabase.create_item_instance("sword_iron")
	
	print("\n剑实例属性:")
	print("  攻击力: %d" % TagUtil.tagi(sword_instance, "attack", 0))
	print("  槽位: %s" % TagUtil.tags(sword_instance, "slot", ""))
	
	# 尝试修改模板（这是错误的做法，但演示为什么不应该这样做）
	print("\n注意：模板的tags应该是只读的！")
	print("模板tags类型: %s" % typeof(sword_template.tags))
	print("实例通过模板访问tags，保持一致性")

## 演示背包操作
func demo_inventory_operations():
	print("\n3. 背包操作演示:")
	
	# 清空背包
	InventoryManager.clear_inventory()
	
	# 添加物品
	print("添加3个生命药水...")
	InventoryManager.add_item("potion_health", 3)
	
	# 检查背包状态
	print("\n背包状态:")
	for i in range(5):  # 只检查前5个槽位
		var slot = InventoryManager.inventory_slots[i]
		if not slot.is_empty():
			var template = slot.get_template()
			print("  槽位%d: %s x%d (模板ID: %s)" % [i, template.name, slot.get_quantity(), slot.template_id])
			
			# 显示每个实例的状态
			for j in range(slot.item_instances.size()):
				var instance = slot.item_instances[j]
				print("    实例%d: 剩余使用次数=%d" % [j, instance.get_remaining_uses()])
	
	# 使用第一个药水
	print("\n使用第一个药水...")
	InventoryManager.use_item(0)
	
	# 再次检查背包状态
	print("\n使用后背包状态:")
	for i in range(5):
		var slot = InventoryManager.inventory_slots[i]
		if not slot.is_empty():
			var template = slot.get_template()
			print("  槽位%d: %s x%d" % [i, template.name, slot.get_quantity()])
			
			for j in range(slot.item_instances.size()):
				var instance = slot.item_instances[j]
				print("    实例%d: 剩余使用次数=%d" % [j, instance.get_remaining_uses()])

## 演示新架构的优势
func demo_architecture_benefits():
	print("\n4. 新架构优势演示:")
	
	print("✓ 物品模板不可变，保证数据一致性")
	print("✓ 实例状态独立，支持个性化属性")
	print("✓ 清晰的职责分离：")
	print("  - ItemTemplate: 存储固有属性（攻击力、槽位等）")
	print("  - ItemInstance: 存储可变状态（使用次数、耐久度等）")
	print("  - InventorySlot: 管理位置和数量")
	print("✓ TagUtil支持两种类型，向后兼容")
	print("✓ 背包操作更加清晰和安全")
