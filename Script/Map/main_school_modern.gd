extends Node2D

# 现代版学校场景脚本
# 负责传送功能和场景初始化

func _ready():
	_logger("现代版学校场景脚本已准备就绪")
	
	# 检查是否有保存的传送位置
	var saved_position = SaveManager.get_teleport_position()
	if saved_position != Vector2.ZERO:
		var player = $TestRunner
		if player:
			player.global_position = saved_position
			_logger("玩家位置已恢复到: %s" % saved_position)
		SaveManager.clear_teleport_position()
	
	# 添加返回按钮
	_add_return_button()

func _add_return_button():
	# 创建HUD
	var hud = CanvasLayer.new()
	hud.name = "HUD"
	add_child(hud)
	
	# 创建返回按钮
	var return_button = Button.new()
	return_button.name = "ReturnButton"
	return_button.text = "返回原版"
	return_button.anchors_preset = Control.PRESET_TOP_RIGHT
	return_button.anchor_left = 1.0
	return_button.anchor_right = 1.0
	return_button.offset_left = -150.0
	return_button.offset_bottom = 50.0
	return_button.pressed.connect(_on_return_button_pressed)
	hud.add_child(return_button)
	_logger("返回按钮已创建")

func _on_return_button_pressed():
	# 获取玩家当前位置
	var player = $TestRunner
	if player:
		var player_position = player.global_position
		# 保存玩家位置到SaveManager
		SaveManager.set_teleport_position(player_position)
		# 传送回原版场景
		SceneManager.fade_to_scene("res://Scene/Map/main_school.tscn")

func _logger(msg: String):
	print("[现代版学校场景] %s" % msg)
