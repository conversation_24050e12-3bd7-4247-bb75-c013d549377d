extends Node2D

# 原版学校场景脚本
# 负责传送功能和场景初始化

func _ready():
	_logger("原版学校场景脚本已准备就绪")
	
	# 检查是否有保存的传送位置
	var saved_position = SaveManager.get_teleport_position()
	if saved_position != Vector2.ZERO:
		var player = $TestRunner
		if player:
			player.global_position = saved_position
			_logger("玩家位置已恢复到: %s" % saved_position)
		SaveManager.clear_teleport_position()
	
	# 添加传送按钮
	_add_teleport_button()

func _add_teleport_button():
	# 创建HUD
	var hud = CanvasLayer.new()
	hud.name = "HUD"
	add_child(hud)
	
	# 创建传送按钮
	var teleport_button = Button.new()
	teleport_button.name = "TeleportButton"
	teleport_button.text = "传送到现代版"
	teleport_button.anchors_preset = Control.PRESET_TOP_RIGHT
	teleport_button.anchor_left = 1.0
	teleport_button.anchor_right = 1.0
	teleport_button.offset_left = -150.0
	teleport_button.offset_bottom = 50.0
	teleport_button.pressed.connect(_on_teleport_button_pressed)
	hud.add_child(teleport_button)
	_logger("传送按钮已创建")

func _on_teleport_button_pressed():
	# 获取玩家当前位置
	var player = $TestRunner
	if player:
		var player_position = player.global_position
		# 保存玩家位置到SaveManager
		SaveManager.set_teleport_position(player_position)
		# 使用shatter效果传送到现代版场景
		SceneManager.shatter_to_scene(
			"res://Scene/Map/main_school_modern.tscn",
			2.0,  # shatter持续时间
		)

func _logger(msg: String):
	print("[原版学校场景] %s" % msg)
