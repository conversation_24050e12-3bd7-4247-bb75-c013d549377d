extends <PERSON><PERSON><PERSON><PERSON><PERSON>

@onready var _setting_button: Button = %SettingButton
@onready var _quit_button: Button = %QuitButton
@onready var _start_game_button: Button = %NewGameButton

func _ready() -> void:
	_setting_button.pressed.connect(_on_setting_button_pressed)
	_quit_button.pressed.connect(_on_quit_button_pressed)
	_start_game_button.pressed.connect(_on_start_game_button_pressed)

	## DEBUG
	if !SceneManager.current_scene:
		SceneManager.current_scene = get_tree().current_scene

func _on_setting_button_pressed():
	GameManager.open_settings()

func _on_quit_button_pressed():
	GameManager.quit_application()

func _on_start_game_button_pressed():
	GameManager.open_save_select()
