extends <PERSON><PERSON><PERSON><PERSON><PERSON>

@onready var _resume_button: Button = %ResumeButton
@onready var _save_button: Button = %Save<PERSON><PERSON>on
@onready var _load_button: Button = %Load<PERSON>utton
@onready var _settings_button: Button = %Set<PERSON><PERSON><PERSON><PERSON>on
@onready var _main_menu_button: Button = %MainMenuButton
@onready var _quit_button: Button = %QuitButton

signal resume_requested
signal save_requested
signal load_requested
signal settings_requested
signal main_menu_requested
signal quit_requested

func _ready():
	_connect_signals()
	
	# 检查是否有可用的存档来启用/禁用按钮
	_update_button_states()

func _connect_signals():
	_resume_button.pressed.connect(_on_resume_button_pressed)
	_save_button.pressed.connect(_on_save_button_pressed)
	_load_button.pressed.connect(_on_load_button_pressed)
	_settings_button.pressed.connect(_on_settings_button_pressed)
	_main_menu_button.pressed.connect(_on_main_menu_button_pressed)
	_quit_button.pressed.connect(_on_quit_button_pressed)

func _update_button_states():
	# 检查是否有当前存档可以恢复
	var can_reload_current = false
	if SaveManager:
		var current_slot = SaveManager.get_current_save_slot()
		can_reload_current = current_slot > 0 and SaveManager.has_save(current_slot)
	
	_load_button.disabled = not can_reload_current

func _on_resume_button_pressed():
	resume_requested.emit()

func _on_save_button_pressed():
	save_requested.emit()

func _on_load_button_pressed():
	load_requested.emit()

func _on_settings_button_pressed():
	settings_requested.emit()

func _on_main_menu_button_pressed():
	main_menu_requested.emit()

func _on_quit_button_pressed():
	quit_requested.emit()

# 键盘快捷键支持
func _input(event):
	if not visible or not is_inside_tree():
		return
	
	var viewport = get_viewport()
	if not viewport:
		return
	
	# 检查ESC键
	if event is InputEventKey and event.pressed and event.keycode == KEY_ESCAPE:
		_on_resume_button_pressed()
		viewport.set_input_as_handled()
		return
	
	# 检查menu动作（如果存在）
	if InputMap.has_action("menu") and event.is_action_pressed("menu"):
		_on_resume_button_pressed()
		viewport.set_input_as_handled()
		return
		
	# 检查ui_cancel动作（如果存在）
	if InputMap.has_action("ui_cancel") and event.is_action_pressed("ui_cancel"):
		_on_resume_button_pressed()
		viewport.set_input_as_handled()
