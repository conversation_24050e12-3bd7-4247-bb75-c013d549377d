extends Canvas<PERSON><PERSON>er

@onready var _return_button: Button = %ReturnButton
@onready var _reset_button: Button = %ResetButton

@onready var _master_volume_slider: HSlider = %MasterVolumeSlider
@onready var _master_volume_value: Label = %MasterVolumeValue
@onready var _music_volume_slider: HSlider = %MusicVolumeSlider
@onready var _music_volume_value: Label = %MusicVolumeValue
@onready var _sfx_volume_slider: HSlider = %SfxVolumeSlider
@onready var _sfx_volume_value: Label = %SfxVolumeValue

@onready var _fullscreen_checkbox: CheckBox = %FullscreenCheckBox
@onready var _vsync_checkbox: CheckBox = %VsyncCheckBox
@onready var _resolution_option: OptionButton = %ResolutionOptionButton
@onready var _window_mode_option: OptionButton = %WindowModeOptionButton

@onready var _language_option: OptionButton = %LanguageOptionButton
@onready var _autosave_checkbox: CheckBox = %AutosaveCheckBox
@onready var _autosave_interval_spinbox: SpinBox = %AutosaveIntervalSpinBox

var _resolutions = [
	Vector2i(1920, 1080),
	Vector2i(1600, 900),
	Vector2i(1366, 768),
	Vector2i(1280, 720),
	Vector2i(1024, 768)
]

var _languages = {
	"zh_CN": "中文",
#	"en_US": "English"
}

var _window_modes = [
	{"name": "窗口模式", "mode": DisplayServer.WINDOW_MODE_WINDOWED, "borderless": false},
	{"name": "无边框窗口", "mode": DisplayServer.WINDOW_MODE_WINDOWED, "borderless": true},
	{"name": "全屏模式", "mode": DisplayServer.WINDOW_MODE_FULLSCREEN, "borderless": false}
]

# 拖动状态标志
var _is_dragging_master_volume: bool = false
var _is_dragging_music_volume: bool = false
var _is_dragging_sfx_volume: bool = false

func _ready() -> void:
	# 设置处理模式，使其在游戏暂停时也能正常工作
	process_mode = Node.PROCESS_MODE_ALWAYS
	_setup_ui()
	_connect_signals()
	_load_settings()

# 处理ESC键返回
func _input(event):
	if not visible or not is_inside_tree():
		return

	# 检查ESC键
	if event is InputEventKey and event.pressed and event.keycode == KEY_ESCAPE:
		_on_return_button_pressed()
		# 安全地标记输入为已处理
		var viewport = get_viewport()
		if viewport:
			viewport.set_input_as_handled()

func _setup_ui():
	_setup_resolution_options()
	_setup_language_options()
	_setup_window_mode_options()

func _setup_resolution_options():
	_resolution_option.clear()
	for resolution in _resolutions:
		_resolution_option.add_item("%dx%d" % [resolution.x, resolution.y])

func _setup_language_options():
	_language_option.clear()
	for key in _languages.keys():
		_language_option.add_item(_languages[key])

func _setup_window_mode_options():
	_window_mode_option.clear()
	for window_mode in _window_modes:
		_window_mode_option.add_item(window_mode.name)

func _connect_signals():
	_return_button.pressed.connect(_on_return_button_pressed)
	_reset_button.pressed.connect(_on_reset_button_pressed)
	
	# 音量滑动条信号连接，包括拖动状态检测
	_master_volume_slider.value_changed.connect(_on_master_volume_changed)
	_master_volume_slider.drag_started.connect(_on_master_volume_drag_started)
	_master_volume_slider.drag_ended.connect(_on_master_volume_drag_ended)
	
	_music_volume_slider.value_changed.connect(_on_music_volume_changed)
	_music_volume_slider.drag_started.connect(_on_music_volume_drag_started)
	_music_volume_slider.drag_ended.connect(_on_music_volume_drag_ended)
	
	_sfx_volume_slider.value_changed.connect(_on_sfx_volume_changed)
	_sfx_volume_slider.drag_started.connect(_on_sfx_volume_drag_started)
	_sfx_volume_slider.drag_ended.connect(_on_sfx_volume_drag_ended)
	
	_fullscreen_checkbox.toggled.connect(_on_fullscreen_toggled)
	_vsync_checkbox.toggled.connect(_on_vsync_toggled)
	_resolution_option.item_selected.connect(_on_resolution_selected)
	_window_mode_option.item_selected.connect(_on_window_mode_selected)
	
	_language_option.item_selected.connect(_on_language_selected)
	_autosave_checkbox.toggled.connect(_on_autosave_toggled)
	_autosave_interval_spinbox.value_changed.connect(_on_autosave_interval_changed)

func _load_settings():
	if not SaveManager:
		print("[SettingsUI] SaveManager not found")
		return
	
	# 加载音量设置并同步到AudioServer
	var master_volume = SaveManager.get_master_volume()
	_master_volume_slider.value = master_volume
	_update_volume_label(_master_volume_value, master_volume)
	_apply_master_volume(master_volume)
	
	var music_volume = SaveManager.get_music_volume()
	_music_volume_slider.value = music_volume
	_update_volume_label(_music_volume_value, music_volume)
	_apply_music_volume(music_volume)
	
	var sfx_volume = SaveManager.get_sfx_volume()
	_sfx_volume_slider.value = sfx_volume
	_update_volume_label(_sfx_volume_value, sfx_volume)
	_apply_sfx_volume(sfx_volume)
	
	_fullscreen_checkbox.button_pressed = SaveManager.is_fullscreen()
	_vsync_checkbox.button_pressed = SaveManager.is_vsync_enabled()
	
	var current_resolution = Vector2i(
		SaveManager.get_setting("resolution_width", "display"),
		SaveManager.get_setting("resolution_height", "display")
	)
	_set_resolution_selection(current_resolution)
	
	var window_mode = SaveManager.get_window_mode()
	var borderless = SaveManager.is_borderless()
	_set_window_mode_selection_with_borderless(window_mode, borderless)
	
	var language = SaveManager.get_language()
	_set_language_selection(language)
	
	_autosave_checkbox.button_pressed = SaveManager.get_setting("autosave_enabled")
	_autosave_interval_spinbox.value = SaveManager.get_setting("autosave_interval")

func _set_resolution_selection(resolution: Vector2i):
	for i in range(_resolutions.size()):
		if _resolutions[i] == resolution:
			_resolution_option.selected = i
			return
	_resolution_option.selected = 0

func _set_language_selection(language: String):
	var keys = _languages.keys()
	for i in range(keys.size()):
		if keys[i] == language:
			_language_option.selected = i
			return
	_language_option.selected = 0

func _set_window_mode_selection_with_borderless(window_mode: int, borderless: bool):
	# 根据window_mode和borderless值设置选项
	if window_mode == DisplayServer.WINDOW_MODE_FULLSCREEN:
		_window_mode_option.selected = 2  # 全屏模式
	elif borderless:
		_window_mode_option.selected = 1  # 无边框窗口
	else:
		_window_mode_option.selected = 0  # 默认窗口模式

func _set_window_mode_selection(window_mode: int):
	# 根据window_mode值设置选项
	if window_mode == DisplayServer.WINDOW_MODE_FULLSCREEN:
		_window_mode_option.selected = 2  # 全屏模式
	else:
		# 检查是否为无边框窗口
		var is_borderless = DisplayServer.window_get_flag(DisplayServer.WINDOW_FLAG_BORDERLESS)
		if is_borderless:
			_window_mode_option.selected = 1  # 无边框窗口
		else:
			_window_mode_option.selected = 0  # 默认窗口模式

func _update_volume_label(label: Label, value: float):
	label.text = "%d%%" % (value * 100)

# 主音量拖动状态管理
func _on_master_volume_drag_started():
	_is_dragging_master_volume = true

func _on_master_volume_drag_ended(value_changed: bool):
	_is_dragging_master_volume = false
	if value_changed:
		# 拖动结束时保存最终值
		SaveManager.set_master_volume(_master_volume_slider.value)

func _on_master_volume_changed(value: float):
	_update_volume_label(_master_volume_value, value)
	_apply_master_volume(value)
	
	# 如果不是拖动状态，立即保存
	if not _is_dragging_master_volume:
		SaveManager.set_master_volume(value)

# 音乐音量拖动状态管理
func _on_music_volume_drag_started():
	_is_dragging_music_volume = true

func _on_music_volume_drag_ended(value_changed: bool):
	_is_dragging_music_volume = false
	if value_changed:
		# 拖动结束时保存最终值
		SaveManager.set_music_volume(_music_volume_slider.value)

func _on_music_volume_changed(value: float):
	_update_volume_label(_music_volume_value, value)
	_apply_music_volume(value)
	
	# 如果不是拖动状态，立即保存
	if not _is_dragging_music_volume:
		SaveManager.set_music_volume(value)

# 音效音量拖动状态管理
func _on_sfx_volume_drag_started():
	_is_dragging_sfx_volume = true

func _on_sfx_volume_drag_ended(value_changed: bool):
	_is_dragging_sfx_volume = false
	if value_changed:
		# 拖动结束时保存最终值
		SaveManager.set_sfx_volume(_sfx_volume_slider.value)

func _on_sfx_volume_changed(value: float):
	_update_volume_label(_sfx_volume_value, value)
	_apply_sfx_volume(value)
	
	# 如果不是拖动状态，立即保存
	if not _is_dragging_sfx_volume:
		SaveManager.set_sfx_volume(value)

func _on_fullscreen_toggled(pressed: bool):
	SaveManager.set_fullscreen(pressed)
	_apply_fullscreen(pressed)

func _on_vsync_toggled(pressed: bool):
	SaveManager.set_vsync_enabled(pressed)
	_apply_vsync(pressed)

func _on_resolution_selected(index: int):
	if index >= 0 and index < _resolutions.size():
		var resolution = _resolutions[index]
		SaveManager.set_setting("resolution_width", resolution.x, "display")
		SaveManager.set_setting("resolution_height", resolution.y, "display")
		_apply_resolution(resolution)

func _on_window_mode_selected(index: int):
	if index >= 0 and index < _window_modes.size():
		var window_mode_config = _window_modes[index]
		SaveManager.set_window_mode(window_mode_config.mode)
		SaveManager.set_borderless(window_mode_config.borderless)
		_apply_window_mode(window_mode_config.mode, window_mode_config.borderless)

func _on_language_selected(index: int):
	var keys = _languages.keys()
	if index >= 0 and index < keys.size():
		var language = keys[index]
		SaveManager.set_language(language)
		_apply_language(language)

func _on_autosave_toggled(pressed: bool):
	SaveManager.set_setting("autosave_enabled", pressed)
	SaveManager.set_autosave_enabled(pressed)

func _on_autosave_interval_changed(value: float):
	SaveManager.set_setting("autosave_interval", value)
	SaveManager.set_autosave_interval(value)

func _apply_master_volume(value: float):
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index("Master"), linear_to_db(value))

func _apply_music_volume(value: float):
	var music_bus = AudioServer.get_bus_index("Music")
	if music_bus != -1:
		AudioServer.set_bus_volume_db(music_bus, linear_to_db(value))

func _apply_sfx_volume(value: float):
	var sfx_bus = AudioServer.get_bus_index("SFX")
	if sfx_bus != -1:
		AudioServer.set_bus_volume_db(sfx_bus, linear_to_db(value))

func _apply_fullscreen(enabled: bool):
	if enabled:
		DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
	else:
		DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_WINDOWED)

func _apply_vsync(enabled: bool):
	if enabled:
		DisplayServer.window_set_vsync_mode(DisplayServer.VSYNC_ENABLED)
	else:
		DisplayServer.window_set_vsync_mode(DisplayServer.VSYNC_DISABLED)

func _apply_resolution(resolution: Vector2i):
	if not SaveManager.is_fullscreen():
		DisplayServer.window_set_size(resolution)

func _apply_window_mode(mode: DisplayServer.WindowMode, borderless: bool):
	# 先设置窗口模式
	DisplayServer.window_set_mode(mode)
	
	# 设置边框属性（只在窗口模式下有效）
	if mode == DisplayServer.WINDOW_MODE_WINDOWED:
		DisplayServer.window_set_flag(DisplayServer.WINDOW_FLAG_BORDERLESS, borderless)
	
	# 更新全屏复选框状态
	var is_fullscreen = (mode == DisplayServer.WINDOW_MODE_FULLSCREEN)
	_fullscreen_checkbox.button_pressed = is_fullscreen
	SaveManager.set_fullscreen(is_fullscreen)

func _apply_language(language: String):
	print("[SettingsUI] Language changed to: %s" % language)

func _on_reset_button_pressed():
	SaveManager.reset_settings()
	_load_settings()
	print("[SettingsUI] Settings reset to defaults")
	
func _on_return_button_pressed():
	GameManager.back_from_settings()
