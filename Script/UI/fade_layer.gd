extends Canvas<PERSON>ayer

@onready var rect: ColorRect = $ColorRect

# Layer管理配置
const DEFAULT_LAYER = 128
const SHATTER_SUPPORT_LAYER = 150  # 支持shatter效果时的layer

func _ready() -> void:
	layer = DEFAULT_LAYER
	rect.visible = false

func fade_in(time := 0.5):
	rect.visible = true
	rect.modulate.a = 1.0
	var tween = create_tween()
	tween.tween_property(rect, "modulate:a", 0.3, time)
	await tween.finished
	rect.visible = false

func fade_out(time := 0.5):
	rect.visible = true
	rect.modulate.a = 0.3
	var tween = create_tween()
	tween.tween_property(rect, "modulate:a", 1.0, time)
	await tween.finished
