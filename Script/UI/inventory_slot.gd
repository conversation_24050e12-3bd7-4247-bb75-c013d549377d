extends Button
## 背包槽位 UI 组件

@onready var background: Panel        = %Background
@onready var item_icon: TextureRect   = %ItemIcon
@onready var quantity_label: Label    = %QuantityLabel
@onready var rarity_border: NinePatchRect = %RarityBorder

var slot_index: int = -1
var inventory_slot: InventoryManager.InventorySlot = null

var rarity_colors := {
						 0: Color.WHITE,
						 1: Color.GREEN,
						 2: Color.BLUE,
						 3: Color.PURPLE,
					 }

func _ready() -> void:
	_setup_background_style()
	mouse_entered.connect(_on_mouse_entered)
	mouse_exited.connect(_on_mouse_exited)
	_clear_display()

func _setup_background_style() -> void:
	if not background: return
	var style := StyleBoxFlat.new()
	style.bg_color = Color(0.2,0.2,0.2,0.8)
	style.border_color = Color(0.6,0.6,0.6)
	style.border_width_left = 2
	style.border_width_right = 2
	style.border_width_top = 2
	style.border_width_bottom = 2
	style.corner_radius_top_left = 4
	style.corner_radius_top_right = 4
	style.corner_radius_bottom_left = 4
	style.corner_radius_bottom_right = 4
	background.add_theme_stylebox_override("panel", style)

func setup_slot(idx:int, slot:InventoryManager.InventorySlot) -> void:
	slot_index = idx
	inventory_slot = slot
	_update_display()

func _update_display() -> void:
	if not inventory_slot or inventory_slot.is_empty():
		_clear_display(); return

	var item = inventory_slot.item

	if item_icon:
		if ResourceLoader.exists(item.icon_path):
			item_icon.texture = load(item.icon_path)
		else:
			_set_default_icon(item)

	if quantity_label:
		if inventory_slot.quantity > 1:
			quantity_label.text = str(inventory_slot.quantity)
		else:
			quantity_label.text = ""

	_set_rarity_border(item.rarity)
	tooltip_text = _generate_tooltip(item)

func _clear_display() -> void:
	if item_icon: item_icon.texture = null
	if quantity_label: 
		quantity_label.visible = true
		quantity_label.text = ""
	if rarity_border: rarity_border.visible = false
	tooltip_text = ""

func _set_default_icon(item:InventoryManager.Item) -> void:
	if not item_icon: return
	var img := Image.create(64,64,false,Image.FORMAT_RGB8)
	img.fill(_get_type_color(item.type))
	var tex := ImageTexture.create_from_image(img)
	item_icon.texture = tex

func _get_type_color(t:InventoryManager.ItemType) -> Color:
	match t:
		InventoryManager.ItemType.CONSUMABLE: return Color.RED
		InventoryManager.ItemType.EQUIPMENT : return Color.YELLOW
		InventoryManager.ItemType.MATERIAL  : return Color.BROWN
		InventoryManager.ItemType.KEY_ITEM  : return Color.GOLD
		_: return Color.GRAY

func _set_rarity_border(r:int) -> void:
	if not rarity_border: return
	if r > 0 and rarity_colors.has(r):
		rarity_border.visible = true
		rarity_border.modulate = rarity_colors[r]
	else:
		rarity_border.visible = false

func _generate_tooltip(item:InventoryManager.Item) -> String:
	var tip := item.name
	if inventory_slot.quantity > 1: tip += " x%s" % inventory_slot.quantity
	tip += "\n" + item.description
	if item is InventoryManager.Equipment:
		var eq := item as InventoryManager.Equipment
		tip += "\n\n属性:"
		for s in eq.stats: tip += "\n%s: +%s" % [s, eq.stats[s]]
		tip += "\n等级需求: %d" % eq.level_requirement
	if item.value > 0: tip += "\n价值: %d 金币" % item.value
	return tip

func set_selected(sel:bool) -> void:
	_update_bg_style(Color.YELLOW if sel else Color(0.6,0.6,0.6))

func set_highlighted(hl:bool) -> void:
	_update_bg_style(Color.WHITE if hl else Color(0.6,0.6,0.6))

func _update_bg_style(border_col:Color) -> void:
	if not background: return
	var s := StyleBoxFlat.new()
	s.bg_color = Color(0.2,0.2,0.2,0.8)
	s.border_width_left = 2
	s.border_width_right = 2
	s.border_width_top = 2
	s.border_width_bottom = 2
	s.border_color = border_col
	s.corner_radius_top_left = 4
	s.corner_radius_top_right = 4
	s.corner_radius_bottom_left = 4
	s.corner_radius_bottom_right = 4
	background.add_theme_stylebox_override("panel", s)


func move_item_to_slot(target_slot_index: int) -> void:
	if slot_index != -1 and target_slot_index != -1:
		InventoryManager.move_item(slot_index, target_slot_index)

func _find_drop_target(global_pos: Vector2) -> Button:
	var grid := get_parent()
	while grid and grid.name != "InventoryGrid":
		grid = grid.get_parent()
	if not grid: return null

	for child in grid.get_children():
		if child != self and child is Button and child.has_method("get_slot_index"):
			if Rect2(child.global_position, child.size).has_point(global_pos):
				return child
	return null

func _on_mouse_entered() -> void:
	if inventory_slot and not inventory_slot.is_empty():
		set_highlighted(true)

func _on_mouse_exited() -> void:
	set_highlighted(false)

func get_slot_index() -> int:
	return slot_index

func get_inventory_slot() -> InventoryManager.InventorySlot:
	return inventory_slot

func is_empty() -> bool:
	return !inventory_slot or inventory_slot.is_empty()
