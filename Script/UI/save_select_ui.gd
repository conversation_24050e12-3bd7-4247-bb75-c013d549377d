extends CanvasLayer

## 存档选择界面 - 动态创建存档槽位进行新游戏或继续游戏

@onready var _return_button: Button = %ReturnButton
@onready var _save_slots_container: VBoxContainer = %SaveSlotsVBox

# 动态创建的存档槽数据
var _slot_data: Array[Dictionary] = []

# 存档槽配置
const MIN_SLOTS = 3  # 最少显示3个槽位
const MAX_SLOTS = 10 # 最多显示10个槽位
const SAVE_SLOT_START = 1  # 跳过自动保存槽位0

func _ready() -> void:
	process_mode = Node.PROCESS_MODE_ALWAYS
	_connect_basic_signals()
	_create_dynamic_slots()

# 处理ESC键返回
func _input(event):
	if not visible or not is_inside_tree():
		return

	if event is InputEventKey and event.pressed and event.keycode == KEY_ESCAPE:
		_on_return_button_pressed()
		var viewport = get_viewport()
		if viewport:
			viewport.set_input_as_handled()

## 连接基础信号
func _connect_basic_signals():
	_return_button.pressed.connect(_on_return_button_pressed)

## 动态创建存档槽位
func _create_dynamic_slots():
	# 清理现有槽位
	_clear_existing_slots()

	# 计算需要显示的槽位数量
	var slot_count = _calculate_slot_count()

	# 创建槽位
	for i in range(slot_count):
		var slot_index = i + SAVE_SLOT_START
		_create_save_slot(slot_index)

	# 刷新显示
	_refresh_save_slots()

## 清理现有的槽位
func _clear_existing_slots():
	_slot_data.clear()
	# 清理容器中的所有子节点
	for child in _save_slots_container.get_children():
		child.queue_free()

## 计算需要显示的槽位数量
func _calculate_slot_count() -> int:
	if not SaveManager:
		return MIN_SLOTS

	# 获取已有存档数量
	var existing_saves = 0
	for i in range(SAVE_SLOT_START, MAX_SLOTS + SAVE_SLOT_START):
		if SaveManager.has_save(i):
			existing_saves += 1

	# 至少显示 MIN_SLOTS 个，最多显示 MAX_SLOTS 个
	# 已有存档数量 + 1（用于新建存档）
	var needed_slots = max(MIN_SLOTS, existing_saves + 1)
	return min(needed_slots, MAX_SLOTS)

## 创建单个存档槽
func _create_save_slot(slot_index: int):
	# 创建槽位面板
	var slot_panel = Panel.new()
	slot_panel.name = "SaveSlot" + str(slot_index)
	slot_panel.custom_minimum_size = Vector2(0, 80)

	# 创建水平容器 - 使用正确的布局方式
	var hbox = HBoxContainer.new()
	hbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	hbox.add_theme_constant_override("separation", 10)
	# 设置内边距 - 确保不超出面板边界
	hbox.offset_left = 10
	hbox.offset_top = 10
	hbox.offset_right = -10
	hbox.offset_bottom = -10
	slot_panel.add_child(hbox)

	# 创建信息容器
	var info_vbox = VBoxContainer.new()
	info_vbox.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	info_vbox.size_flags_vertical = Control.SIZE_SHRINK_CENTER
	hbox.add_child(info_vbox)

	# 创建标题标签
	var title_label = Label.new()
	title_label.text = "存档槽 " + str(slot_index)
	title_label.add_theme_font_size_override("font_size", 18)
	title_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	info_vbox.add_child(title_label)

	# 创建时间标签
	var time_label = Label.new()
	time_label.text = "空存档槽"
	time_label.add_theme_color_override("font_color", Color(0.7, 0.7, 0.7, 1))
	time_label.add_theme_font_size_override("font_size", 14)
	info_vbox.add_child(time_label)

	# 创建位置标签
	var location_label = Label.new()
	location_label.add_theme_color_override("font_color", Color(0.6, 0.6, 0.6, 1))
	location_label.add_theme_font_size_override("font_size", 12)
	info_vbox.add_child(location_label)

	# 创建按钮容器 - 关键修复：正确的尺寸约束
	var button_hbox = HBoxContainer.new()
	button_hbox.add_theme_constant_override("separation", 5)
	button_hbox.size_flags_horizontal = Control.SIZE_SHRINK_END
	button_hbox.size_flags_vertical = Control.SIZE_SHRINK_CENTER
	hbox.add_child(button_hbox)

	# 创建操作按钮 - 修复：使用合适的尺寸设置
	var action_button = Button.new()
	action_button.text = "开始新游戏"
	action_button.custom_minimum_size = Vector2(100, 32)  # 减小宽度，设置合适高度
	action_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	action_button.size_flags_vertical = Control.SIZE_SHRINK_CENTER
	action_button.pressed.connect(_on_action_button_pressed.bind(slot_index))
	button_hbox.add_child(action_button)

	# 创建删除按钮 - 修复：使用合适的尺寸设置
	var delete_button = Button.new()
	delete_button.text = "删除"
	delete_button.custom_minimum_size = Vector2(60, 32)  # 减小尺寸
	delete_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	delete_button.size_flags_vertical = Control.SIZE_SHRINK_CENTER
	delete_button.visible = false
	delete_button.pressed.connect(_on_delete_button_pressed.bind(slot_index))
	button_hbox.add_child(delete_button)

	# 保存槽位数据
	var slot_data = {
		"slot_index": slot_index,
		"panel": slot_panel,
		"title_label": title_label,
		"time_label": time_label,
		"location_label": location_label,
		"action_button": action_button,
		"delete_button": delete_button
	}
	_slot_data.append(slot_data)

	# 添加到容器
	_save_slots_container.add_child(slot_panel)

func _refresh_save_slots():
	if not SaveManager:
		print("[SaveSelectUI] SaveManager not available")
		return

	# 获取所有存档信息
	var save_list = SaveManager.get_all_save_info()

	# 更新每个槽位的显示
	for slot_data in _slot_data:
		var slot_index = slot_data.slot_index
		var save_info = null

		# 确保索引在范围内
		if slot_index < save_list.size():
			save_info = save_list[slot_index]

		_update_slot_display(slot_data, save_info)

func _update_slot_display(slot_data: Dictionary, save_info):
	var time_label = slot_data.time_label
	var location_label = slot_data.location_label
	var action_button = slot_data.action_button
	var delete_button = slot_data.delete_button

	var has_save = save_info != null and not save_info.is_empty()

	if not has_save:
		# 空存档槽
		time_label.text = "空存档槽"
		time_label.modulate = Color(0.7, 0.7, 0.7, 1.0)
		location_label.text = ""
		delete_button.visible = false
	else:
		# 有存档数据
		var save_time = save_info.get("save_time", "未知时间")
		var location = save_info.get("location", "unknown")
		var level = save_info.get("level", 1)

		# 格式化时间显示
		time_label.text = _format_save_time(save_time)
		time_label.modulate = Color(0.9, 0.9, 0.9, 1.0)

		# 格式化位置显示
		location_label.text = "等级 " + str(level) + " - " + _format_location(location)
		delete_button.visible = true

	# 设置按钮文本和尺寸
	var button_text = _get_action_button_text(has_save)
	action_button.text = button_text
	_adjust_button_size(action_button, button_text)

## 刷新槽位显示（供外部调用）
func refresh_slots():
	_create_dynamic_slots()

## 获取适合的按钮文本（避免文本过长）
func _get_action_button_text(has_save: bool) -> String:
	if has_save:
		return "继续"  # 缩短文本
	else:
		return "新游戏"  # 缩短文本

## 动态调整按钮尺寸
func _adjust_button_size(button: Button, text: String):
	# 根据文本长度调整按钮最小宽度
	var text_length = text.length()
	var min_width = max(60, text_length * 8 + 20)  # 基于文本长度计算
	min_width = min(min_width, 120)  # 限制最大宽度
	button.custom_minimum_size.x = min_width

func _format_save_time(time_string: String) -> String:
	# 简化时间格式显示
	# 输入格式类似: "2024-01-01T12:30:45"
	if time_string.is_empty() or time_string == "未知时间":
		return "未知时间"
	
	var parts = time_string.split("T")
	if parts.size() >= 2:
		var date_part = parts[0]
		var time_part = parts[1].substr(0, 5)  # 只取小时和分钟
		return date_part + " " + time_part
	
	return time_string

func _format_location(location: String) -> String:
	# 简化场景路径显示
	if location.is_empty() or location == "unknown":
		return "未知位置"
	
	# 从路径中提取文件名
	var file_name = location.get_file().get_basename()
	
	# 根据场景名称返回友好的中文名称
	match file_name:
		"main_school":
			return "学校主区域"
		"classroom":
			return "教室"
		"hallway":
			return "走廊"
		"main_menu":
			return "主菜单"
		_:
			return file_name

func _on_action_button_pressed(slot_index: int):
	if not SaveManager:
		print("[SaveSelectUI] SaveManager not available")
		return
	
	# 检查存档槽是否有数据
	if SaveManager.has_save(slot_index):
		# 有存档，继续游戏
		print("[SaveSelectUI] Continue game from slot " + str(slot_index))
		if GameManager.continue_game(slot_index):
			_close_and_destroy()
		else:
			print("[SaveSelectUI] Failed to continue game from slot " + str(slot_index))
	else:
		# 空槽，开始新游戏
		print("[SaveSelectUI] Start new game in slot " + str(slot_index))
		GameManager.start_new_game(slot_index)
		_close_and_destroy()

func _on_delete_button_pressed(slot_index: int):
	if not SaveManager:
		return
	
	# 删除存档
	if SaveManager.delete_save(slot_index):
		print("[SaveSelectUI] Deleted save in slot " + str(slot_index))
		# 重新创建槽位以反映变化
		_create_dynamic_slots()
	else:
		print("[SaveSelectUI] Failed to delete save in slot " + str(slot_index))

func _on_return_button_pressed():
	# 返回主菜单
	GameManager.go_to_main_menu()
	_close_and_destroy()

## 关闭并销毁UI
func _close_and_destroy():
	hide()
	# 延迟销毁以确保动画完成
	await get_tree().create_timer(0.1).timeout
	queue_free()
	_logger("SaveSelectUI destroyed")

# 显示界面时刷新存档信息
func _on_visibility_changed():
	if visible:
		_create_dynamic_slots()

func _logger(msg: String) -> void:
	print("[SaveSelectUI] " + msg)
