extends CanvasLayer

var texture_rect: TextureRect

func _ready() -> void:
	hide()
	_setup_texture_rect()

func _setup_texture_rect():
	texture_rect = TextureRect.new()
	texture_rect.anchors_preset = Control.PRESET_FULL_RECT
	
	# 加载shader并创建材质
	var shatter_shader = load("res://Asset/Shader/shatter_effect.gdshader")
	if shatter_shader:
		var material = ShaderMaterial.new()
		material.shader = shatter_shader
		texture_rect.material = material
	
	add_child(texture_rect)

func start_effect(duration: float) -> void:
	# 1. Capture the screen content as an Image object.
	#    An Image holds the pixel data on the CPU.
	var image: Image = get_viewport().get_texture().get_image()
	# 2. Create a new, completely independent ImageTexture from the Image data.
	#    This is the crucial step to break the rendering dependency loop.
	#    The new texture is a static snapshot and is no longer linked to the viewport.
	var texture_snapshot: ImageTexture = ImageTexture.create_from_image(image)
	# 3. Assign this static snapshot to our TextureRect.
	texture_rect.texture = texture_snapshot
	# 4. Now that the TextureRect has its content, we can show the effect layer.
	#    Doing this after setting the texture avoids a single frame of flicker.
	show()
	# 5. Set shader parameters and start animation
	var material: ShaderMaterial = texture_rect.material
	
	# 初始化shader参数
	material.set_shader_parameter("progress", 0.0)

	var tween = create_tween().set_pause_mode(Tween.TWEEN_PAUSE_PROCESS)
	tween.tween_property(material, "shader_parameter/progress", 1.0, duration)\
	.set_trans(Tween.TRANS_CUBIC)\
	.set_ease(Tween.EASE_IN_OUT)
	tween.tween_callback(queue_free)
