extends Node

## 背包系统重构测试脚本 (修复版)
## 验证新的TagUtil + StatService + ItemEffectService架构是否正常工作

func _ready():
	_logger("Starting inventory refactor integration test (Fixed Version)...")
	
	# 等待所有服务初始化完成
	await get_tree().process_frame
	await get_tree().process_frame  # 额外等待确保服务连接完成
	
	_run_tests()

func _run_tests():
	_logger("=== 背包系统重构集成测试 (修复版) ===")
	
	# 测试1: ItemDatabase加载
	test_item_database()
	
	# 测试2: TagUtil功能
	test_tag_util()
	
	# 测试3: StatService功能
	test_stat_service()
	
	# 测试4: 服务依赖验证
	test_service_dependencies()
	
	# 测试5: 物品使用逻辑
	test_item_usage_logic()
	
	# 测试6: 装备系统
	test_equipment_system()
	
	# 测试7: 错误处理
	test_error_handling()
	
	_logger("=== 集成测试完成 ===")

func test_service_dependencies():
	_logger("--- 测试服务依赖 ---")
	
	var stat_service = get_stat_service()
	var item_effect_service = get_item_effect_service()
	
	_logger("GameManager服务状态:")
	_logger("  StatService: %s" % ("✅" if stat_service else "❌"))
	_logger("  ItemEffectService: %s" % ("✅" if item_effect_service else "❌"))
	
	if item_effect_service:
		_logger("ItemEffectService依赖:")
		_logger("  内部StatService引用: %s" % ("✅" if item_effect_service.stat_service else "❌"))
		_logger("  内部InventoryManager引用: %s" % ("✅" if item_effect_service.inventory_manager else "❌"))

func test_item_usage_logic():
	_logger("--- 测试物品使用逻辑 ---")
	
	# 先清空背包并添加测试物品
	if InventoryManager:
		InventoryManager.clear_inventory()
		InventoryManager.add_item("potion_health", 3)
		
		# 查看物品标签
		var slot = InventoryManager.get_inventory_slots()[0]
		if not slot.is_empty():
			var item = slot.item
			_logger("测试物品: %s" % item.name)
			_logger("  使用次数: %d" % TagUtil.get_remaining_uses(item))
			_logger("  可使用: %s" % TagUtil.is_usable(item))
			
			# 测试使用物品
			var initial_quantity = slot.quantity
			_logger("使用前数量: %d" % initial_quantity)
			
			var success = InventoryManager.use_item(0)
			_logger("使用结果: %s" % ("成功" if success else "失败"))
			
			var final_quantity = slot.quantity
			_logger("使用后数量: %d" % final_quantity)

func test_error_handling():
	_logger("--- 测试错误处理 ---")
	
	# 测试使用不存在的物品
	var result = InventoryManager.use_item(99)
	_logger("使用不存在槽位物品: %s (预期:false)" % result)
	
	# 测试使用空槽位
	if InventoryManager:
		InventoryManager.clear_inventory()
		result = InventoryManager.use_item(0)
		_logger("使用空槽位: %s (预期:false)" % result)

func test_item_database():
	_logger("--- 测试ItemDatabase ---")
	
	# 检查数据库是否加载
	if not ItemDatabase._initialized:
		ItemDatabase.load_database()
	
	var stats = ItemDatabase.get_statistics()
	_logger("数据库统计: %s" % stats)
	
	# 测试几个关键物品
	var test_items = ["potion_health", "sword_iron", "armor_leather"]
	for item_id in test_items:
		var item = ItemDatabase.get_item(item_id)
		if item:
			_logger("物品加载成功: %s - %s" % [item_id, item.name])
			if item.has("tags"):
				_logger("  标签: %s" % item.tags)
		else:
			_logger("错误: 物品未找到 %s" % item_id)

func test_tag_util():
	_logger("--- 测试TagUtil ---")
	
	# 测试标签解析
	var test_tag_string = "use:1,hp:50,rarity:2"
	var parsed = TagUtil.parse(test_tag_string)
	_logger("标签解析测试: '%s' -> %s" % [test_tag_string, parsed])
	
	# 测试标签编码
	var encoded = TagUtil.encode(parsed)
	_logger("标签编码测试: %s -> '%s'" % [parsed, encoded])
	
	# 测试物品查询功能
	var health_potion = ItemDatabase.get_item("potion_health")
	if health_potion:
		_logger("生命药水可使用: %s" % TagUtil.is_usable(health_potion))
		_logger("生命药水可装备: %s" % TagUtil.is_equipable(health_potion))
	
	var iron_sword = ItemDatabase.get_item("sword_iron")
	if iron_sword:
		_logger("铁剑可使用: %s" % TagUtil.is_usable(iron_sword))
		_logger("铁剑可装备: %s" % TagUtil.is_equipable(iron_sword))
		_logger("铁剑装备槽位: %d" % TagUtil.get_equipment_slot(iron_sword))

func test_stat_service():
	_logger("--- 测试StatService ---")
	
	var stat_service = get_stat_service()
	if not stat_service:
		_logger("错误: StatService未找到")
		return
	
	# 测试基础属性操作
	stat_service.set_stat("hp", 80)
	_logger("设置HP为80: %d" % stat_service.get_stat("hp"))
	
	stat_service.heal(20)
	_logger("治疗20点: HP = %d" % stat_service.get_stat("hp"))
	
	stat_service.modify_stat("attack", 5)
	_logger("增加5点攻击: 攻击力 = %d" % stat_service.get_stat("attack"))
	
	# 测试状态查询
	_logger("是否死亡: %s" % stat_service.is_dead())
	_logger("是否满血: %s" % stat_service.is_full_health())
	_logger("血量百分比: %.1f%%" % (stat_service.get_health_percentage() * 100))

func test_equipment_system():
	_logger("--- 测试装备系统 ---")
	
	var stat_service = get_stat_service()
	var item_effect_service = get_item_effect_service()
	
	if not stat_service or not item_effect_service:
		_logger("错误: 服务未找到")
		return
	
	# 记录初始攻击力
	var initial_attack = stat_service.get_stat("attack")
	_logger("初始攻击力: %d" % initial_attack)
	
	# 模拟装备铁剑
	var iron_sword = ItemDatabase.get_item("sword_iron")
	if iron_sword:
		_logger("装备铁剑...")
		item_effect_service._apply_equipment_effects(iron_sword)
		
		var new_attack = stat_service.get_stat("attack")
		_logger("装备后攻击力: %d (增加 %d)" % [new_attack, new_attack - initial_attack])
		
		# 卸下装备
		_logger("卸下铁剑...")
		item_effect_service._remove_equipment_effects(iron_sword)
		
		var final_attack = stat_service.get_stat("attack")
		_logger("卸下后攻击力: %d" % final_attack)

func get_stat_service() -> StatService:
	if GameManager and GameManager.stat_service:
		return GameManager.stat_service
	return null

func get_item_effect_service() -> ItemEffectService:
	if GameManager and GameManager.item_effect_service:
		return GameManager.item_effect_service
	return null

func _logger(msg: String):
	print("[InventoryRefactorTest] %s" % msg)