extends Node

## 时间效果演示管理器
## 负责演示时间系统的各种功能
## 作为独立模块，确保在整个游戏中只运行一次

class_name TimeEffectDemo

var demo_timer: Timer
var demo_step: int = 0
var demo_started: bool = false
var demo_completed: bool = false

var demo_steps = [
	{"name": "启用黎明效果", "action": "enable_dawn"},
	{"name": "启用正午效果", "action": "enable_noon"}, 
	{"name": "启用黄昏效果", "action": "enable_sunset"},
	{"name": "启用深夜效果", "action": "enable_midnight"},
	{"name": "平滑过渡：深夜到黎明", "action": "transition_night_to_dawn"},
	{"name": "平滑过渡：黎明到正午", "action": "transition_dawn_to_noon"},
	{"name": "平滑过渡：正午到黄昏", "action": "transition_noon_to_sunset"},
	{"name": "平滑过渡：黄昏到深夜", "action": "transition_sunset_to_night"},
	{"name": "关闭时间效果", "action": "disable_effect"},
	{"name": "演示完成", "action": "demo_complete"}
]

## 信号定义
signal demo_step_completed(step_name: String)
signal demo_finished()

func _ready():
	_logger("时间效果演示管理器已准备就绪")
	
	# 创建演示定时器
	demo_timer = Timer.new()
	demo_timer.wait_time = 5.0  # 5秒间隔
	demo_timer.timeout.connect(_on_demo_timer_timeout)
	add_child(demo_timer)

## 开始时间效果演示
func start_demo():
	if demo_started or demo_completed:
		_logger("演示已经运行过，跳过")
		return
	
	_logger("=== 开始时间效果演示 ===")
	demo_started = true
	
	# 10秒后开始演示
	await get_tree().create_timer(10.0).timeout
	_start_demo_sequence()

## 强制重新开始演示（用于测试）
func restart_demo():
	_logger("强制重新开始时间效果演示")
	demo_started = false
	demo_completed = false
	demo_step = 0
	demo_timer.stop()
	start_demo()

## 停止演示
func stop_demo():
	if demo_timer:
		demo_timer.stop()
	_logger("时间效果演示已停止")

## 检查演示是否已完成
func is_demo_completed() -> bool:
	return demo_completed

## 开始演示序列
func _start_demo_sequence():
	demo_step = 0
	_execute_demo_step()
	demo_timer.start()

func _on_demo_timer_timeout():
	demo_step += 1
	if demo_step < demo_steps.size():
		_execute_demo_step()
	else:
		demo_timer.stop()
		demo_completed = true
		_logger("=== 演示结束 ===")
		demo_finished.emit()

func _execute_demo_step():
	var step = demo_steps[demo_step]
	_logger("[演示步骤 %d/%d] %s" % [demo_step + 1, demo_steps.size(), step.name])
	
	match step.action:
		"enable_dawn":
			GameManager.enable_time_effect(6.0)  # 黎明6点
		
		"enable_noon":
			GameManager.disable_time_effect()
			await get_tree().process_frame
			GameManager.enable_time_effect(12.0)  # 正午12点
		
		"enable_sunset":
			GameManager.disable_time_effect()
			await get_tree().process_frame
			GameManager.enable_time_effect(18.0)  # 黄昏18点
		
		"enable_midnight":
			GameManager.disable_time_effect()
			await get_tree().process_frame
			GameManager.enable_time_effect(0.0)  # 深夜0点
		
		"transition_night_to_dawn":
			GameManager.smooth_time_transition(0.0, 6.0, 4.0)  # 4秒过渡
		
		"transition_dawn_to_noon":
			GameManager.smooth_time_transition(6.0, 12.0, 4.0)  # 4秒过渡
		
		"transition_noon_to_sunset":
			GameManager.smooth_time_transition(12.0, 18.0, 4.0)  # 4秒过渡
		
		"transition_sunset_to_night":
			GameManager.smooth_time_transition(18.0, 24.0, 4.0)  # 4秒过渡
		
		"disable_effect":
			GameManager.disable_time_effect()
		
		"demo_complete":
			_logger("所有时间效果功能演示完成！")
	
	# 发送步骤完成信号
	demo_step_completed.emit(step.name)

func _logger(msg: String):
	print("[时间效果演示] %s" % msg)