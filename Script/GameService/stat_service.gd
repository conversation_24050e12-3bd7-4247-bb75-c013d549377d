extends Node

## 统计服务 - 管理角色属性和状态
## 作为业务逻辑层，提供统一的属性操作接口

class_name StatService

# ================================
# 信号定义
# ================================

signal stats_changed(stat_name: String, old_value, new_value)
signal stat_updated(stat_name: String, current_value, max_value)

# ================================
# 属性数据
# ================================

var stats_data: Dictionary = {}
var max_stats: Dictionary = {}

# ================================
# 默认属性配置
# ================================

const DEFAULT_STATS = {
	"hp": 100,
	"max_hp": 100,
	"mp": 50,
	"max_mp": 50,
	"attack": 10,
	"defense": 5,
	"speed": 10,
	"level": 1,
	"experience": 0,
	"coins": 0
}

# ================================
# 初始化
# ================================

func _ready():
	_initialize_stats()
	print("[StatService] StatService initialized")

func _initialize_stats():
	# 初始化默认属性
	for stat_name in DEFAULT_STATS:
		stats_data[stat_name] = DEFAULT_STATS[stat_name]
	
	# 设置最大值映射
	max_stats["hp"] = "max_hp"
	max_stats["mp"] = "max_mp"
	
	print("[StatService] Default stats initialized")

# ================================
# 属性操作核心接口
# ================================

## 设置属性值
func set_stat(stat_name: String, value) -> bool:
	if not stats_data.has(stat_name):
		print("[StatService] Warning: Unknown stat '%s'" % stat_name)
		return false
	
	var old_value = stats_data[stat_name]
	
	# 对有最大值限制的属性进行约束
	if max_stats.has(stat_name):
		var max_stat_name = max_stats[stat_name]
		var max_value = stats_data.get(max_stat_name, value)
		value = clamp(value, 0, max_value)
	else:
		# 确保非负值（对大多数属性而言）
		if stat_name in ["hp", "mp", "attack", "defense", "speed", "level", "experience", "coins"]:
			value = max(0, value)
	
	stats_data[stat_name] = value
	
	# 发送信号通知变化
	if old_value != value:
		stats_changed.emit(stat_name, old_value, value)
		
		# 如果是有最大值的属性，也发送更新信号
		if max_stats.has(stat_name):
			var max_value = stats_data.get(max_stats[stat_name], value)
			stat_updated.emit(stat_name, value, max_value)
		
		_log_stat_change(stat_name, old_value, value)
	
	return true

## 获取属性值
func get_stat(stat_name: String, default_value = 0):
	return stats_data.get(stat_name, default_value)

## 修改属性值（相对变化）
func modify_stat(stat_name: String, delta) -> bool:
	var current_value = get_stat(stat_name, 0)
	return set_stat(stat_name, current_value + delta)

## 检查属性是否存在
func has_stat(stat_name: String) -> bool:
	return stats_data.has(stat_name)

# ================================
# 专用属性操作函数
# ================================

## 治疗生命值
func heal(amount: int) -> bool:
	return modify_stat("hp", amount)

## 恢复魔法值
func restore_mana(amount: int) -> bool:
	return modify_stat("mp", amount)

## 造成伤害
func take_damage(damage: int) -> bool:
	return modify_stat("hp", -damage)

## 消耗魔法
func consume_mana(cost: int) -> bool:
	var current_mp = get_stat("mp", 0)
	if current_mp >= cost:
		return modify_stat("mp", -cost)
	return false

## 增加经验值
func add_experience(exp: int) -> bool:
	return modify_stat("experience", exp)

## 增加金币
func add_coins(amount: int) -> bool:
	return modify_stat("coins", amount)

## 扣除金币
func spend_coins(amount: int) -> bool:
	var current_coins = get_stat("coins", 0)
	if current_coins >= amount:
		return modify_stat("coins", -amount)
	return false

# ================================
# 最大值管理
# ================================

## 设置最大生命值
func set_max_hp(value: int) -> bool:
	var result = set_stat("max_hp", value)
	# 确保当前HP不超过新的最大值
	var current_hp = get_stat("hp", 0)
	if current_hp > value:
		set_stat("hp", value)
	return result

## 设置最大魔法值
func set_max_mp(value: int) -> bool:
	var result = set_stat("max_mp", value)
	# 确保当前MP不超过新的最大值
	var current_mp = get_stat("mp", 0)
	if current_mp > value:
		set_stat("mp", value)
	return result

# ================================
# 状态查询
# ================================

## 检查是否死亡
func is_dead() -> bool:
	return get_stat("hp", 0) <= 0

## 检查是否满血
func is_full_health() -> bool:
	return get_stat("hp", 0) >= get_stat("max_hp", 100)

## 检查是否满魔法
func is_full_mana() -> bool:
	return get_stat("mp", 0) >= get_stat("max_mp", 50)

## 检查是否有足够金币
func can_afford(cost: int) -> bool:
	return get_stat("coins", 0) >= cost

## 获取生命值百分比
func get_health_percentage() -> float:
	var current_hp = get_stat("hp", 0)
	var max_hp = get_stat("max_hp", 100)
	if max_hp <= 0:
		return 0.0
	return float(current_hp) / float(max_hp)

## 获取魔法值百分比
func get_mana_percentage() -> float:
	var current_mp = get_stat("mp", 0)
	var max_mp = get_stat("max_mp", 50)
	if max_mp <= 0:
		return 0.0
	return float(current_mp) / float(max_mp)

# ================================
# 批量操作
# ================================

## 批量设置属性（用于加载存档等）
func set_stats_batch(stats_dict: Dictionary):
	for stat_name in stats_dict:
		set_stat(stat_name, stats_dict[stat_name])
	print("[StatService] Batch stats loaded: %s" % stats_dict.keys())

## 获取所有属性数据
func get_all_stats() -> Dictionary:
	return stats_data.duplicate()

## 重置所有属性到默认值
func reset_to_defaults():
	for stat_name in DEFAULT_STATS:
		set_stat(stat_name, DEFAULT_STATS[stat_name])
	print("[StatService] Stats reset to defaults")

# ================================
# 存档系统集成
# ================================

## 获取存档数据
func get_save_data() -> Dictionary:
	return {
		"stats_data": stats_data.duplicate(),
		"max_stats": max_stats.duplicate()
	}

## 加载存档数据
func load_save_data(save_data: Dictionary):
	if save_data.has("stats_data"):
		stats_data = save_data["stats_data"].duplicate()
	
	if save_data.has("max_stats"):
		max_stats = save_data["max_stats"].duplicate()
	
	# 确保所有默认属性都存在
	for stat_name in DEFAULT_STATS:
		if not stats_data.has(stat_name):
			stats_data[stat_name] = DEFAULT_STATS[stat_name]
	
	# 发送所有属性变化信号
	for stat_name in stats_data:
		stats_changed.emit(stat_name, null, stats_data[stat_name])
		if max_stats.has(stat_name):
			var max_value = stats_data.get(max_stats[stat_name], stats_data[stat_name])
			stat_updated.emit(stat_name, stats_data[stat_name], max_value)
	
	print("[StatService] Save data loaded successfully")

# ================================
# 与GameManager的兼容接口
# ================================

## 更新GameManager的player_data（向后兼容）
func sync_to_game_manager():
	if GameManager and GameManager.has_method("update_player_data"):
		for stat_name in stats_data:
			GameManager.update_player_data(stat_name, stats_data[stat_name])
		print("[StatService] Synced to GameManager")

## 从GameManager加载数据（迁移用）
func load_from_game_manager():
	if GameManager and GameManager.has("player_data"):
		var player_data = GameManager.player_data
		for stat_name in player_data:
			if DEFAULT_STATS.has(stat_name):
				set_stat(stat_name, player_data[stat_name])
		print("[StatService] Loaded from GameManager: %s" % player_data.keys())

# ================================
# 调试和工具函数
# ================================

## 打印所有属性状态
func debug_print_stats():
	print("[StatService] === Current Stats ===")
	for stat_name in stats_data:
		var value = stats_data[stat_name]
		if max_stats.has(stat_name):
			var max_value = stats_data.get(max_stats[stat_name], value)
			print("  %s: %s / %s" % [stat_name, value, max_value])
		else:
			print("  %s: %s" % [stat_name, value])

## 记录属性变化日志
func _log_stat_change(stat_name: String, old_value, new_value):
	print("[StatService] %s: %s -> %s" % [stat_name, old_value, new_value])

## 添加自定义属性
func add_custom_stat(stat_name: String, initial_value = 0, max_stat_name: String = ""):
	if not stats_data.has(stat_name):
		stats_data[stat_name] = initial_value
		
		if not max_stat_name.is_empty():
			max_stats[stat_name] = max_stat_name
			# 如果最大值属性不存在，创建它
			if not stats_data.has(max_stat_name):
				stats_data[max_stat_name] = initial_value
		
		print("[StatService] Added custom stat: %s = %s" % [stat_name, initial_value])
		stats_changed.emit(stat_name, null, initial_value)
		return true
	
	print("[StatService] Stat '%s' already exists" % stat_name)
	return false

## 移除自定义属性
func remove_custom_stat(stat_name: String) -> bool:
	if stats_data.has(stat_name) and not DEFAULT_STATS.has(stat_name):
		var old_value = stats_data[stat_name]
		stats_data.erase(stat_name)
		
		# 如果有对应的最大值映射，也移除
		if max_stats.has(stat_name):
			max_stats.erase(stat_name)
		
		print("[StatService] Removed custom stat: %s" % stat_name)
		stats_changed.emit(stat_name, old_value, null)
		return true
	
	return false