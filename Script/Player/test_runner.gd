class_name <PERSON><PERSON><PERSON><PERSON> extends CharacterBody2D


## The top speed that the runner can achieve
@export var max_speed := 600.0
## How much speed is added per second when the player presses a movement key
@export var acceleration := 1200.0
## How much speed is lost per second when the player releases all movement keys
#ANCHOR:var_deceleration
@export var deceleration := 1080.0
#END:var_deceleration
#END:file_header

#ANCHOR:character_visual
@onready var _runner_visual: RunnerVisual = %RunnerVisualPurple
#END:character_visual

#ANCHOR:dust_ref
@onready var _dust: GPUParticles2D = %Dust
#END:dust_ref

#ANCHOR:walked_to_signal
## Emitted when the character has walked to the specified destination.
signal walked_to
#END:walked_to_signal


#ANCHOR:physics_process
func _physics_process(delta: float) -> void:
#ANCHOR:physics_process_body
	#ANCHOR:direction_calculation
	var direction := Input.get_vector("move_left", "move_right", "move_up", "move_down")
	#END:direction_calculation
	#ANCHOR:desired_velocity
	var has_input_direction := direction.length() > 0.0
	if has_input_direction:
		#ANCHOR:desired_velocity_var_only
		var desired_velocity := direction * max_speed
		#END:desired_velocity_var_only
		velocity = velocity.move_toward(desired_velocity, acceleration * delta)
	else:
		velocity = velocity.move_toward(Vector2.ZERO, deceleration * delta)
	#END:desired_velocity

	move_and_slide()

	#ANCHOR:animation_condition
	if direction.length() > 0.0:
		#END:animation_condition
		_runner_visual.angle = rotate_toward(_runner_visual.angle, direction.orthogonal().angle(), 8.0 * delta)

		#ANCHOR:animation_run
		var current_speed_percent := velocity.length() / max_speed
		_runner_visual.animation_name = (
			RunnerVisual.Animations.WALK
			if current_speed_percent < 0.8
			else RunnerVisual.Animations.RUN
		)
		#END:animation_run
		_dust.emitting = true
	else:
		_runner_visual.animation_name = RunnerVisual.Animations.IDLE
		_dust.emitting = false
#END:physics_process_body
#END:physics_process

#ANCHOR:walk_to_function
## Forces the character to walk to a position ignoring collisions[br]
## [param destination_global_position]: The desired destination, given in global coordinates.
#ANCHOR:walk_to_signature
func walk_to(destination_global_position: Vector2) -> void:
#END:walk_to_signature
	# obtain the direction and angle
#ANCHOR:direction
	var direction := global_position.direction_to(destination_global_position)
	_runner_visual.angle = direction.orthogonal().angle()
#END:direction

	# Set the proper animation name
#ANCHOR:animation
	_runner_visual.animation_name = RunnerVisual.Animations.WALK
	_dust.emitting = true
#END:animation

	# obtain distance, and calculate direction from that
#ANCHOR:duration
	var distance := global_position.distance_to(destination_global_position)
	var duration :=  distance / (max_speed * 0.2)
#END:duration

	# tween the runner to destination, then emit `walked_to`
#ANCHOR:walk_to_tween
	var tween := create_tween()
	tween.tween_property(self, "global_position", destination_global_position, duration)
	tween.finished.connect(func():
		_runner_visual.animation_name = RunnerVisual.Animations.IDLE
		_dust.emitting = false
		walked_to.emit()
	)
#END:walk_to_tween
#END:walk_to_function
