extends Control
# fade_duration 1.5 display_duration 1.0
@export var fade_duration := 1.5        # 淡入/淡出时间（秒）
@export var display_duration := 1.0    # 中间停留时间
@export var next_scene_path := "res://Scene/main_menu.tscn"

@onready var _label: Label = $Label

func _ready() -> void:
	_label.modulate = Color(1.0, 1.0, 1.0, 0.0)
	var tween := get_tree().create_tween()
	tween.tween_property(_label, "modulate:a", 1.0, fade_duration)
	tween.tween_interval(display_duration)
	tween.tween_property(_label, "modulate:a", 0.0, fade_duration)
	tween.tween_callback(_on_fade_finished)
	SceneManager.current_scene = get_tree().current_scene
	
func _on_fade_finished():
	# 通过GameManager进入主菜单状态，保持架构一致性
	GameManager.go_to_main_menu()
