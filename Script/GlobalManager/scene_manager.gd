extends Node

## 当前场景的引用，方便管理和销毁
var current_scene: Node = null

## UI层管理
var ui_layers: Array[CanvasLayer] = []

## 暂停菜单管理 - 从 GameManager 迁移过来
var pause_menu: CanvasLayer = null
var is_pause_menu_active: bool = false

## 场景切换完成信号
signal scene_changed(old_scene_path: String, new_scene_path: String)

## 场景路径常量
const MAIN_MENU_SCENE = "res://Scene/main_menu.tscn"
const SETTINGS_SCENE = "res://Scene/UI/settings_ui.tscn"
const PAUSE_MENU_SCENE = "res://Scene/UI/pause_menu.tscn"
const MAIN_SCHOOL_SCENE = "res://Scene/Map/main_school.tscn"

## 设置UI预加载（作为UI层而不是场景）
@onready var settings_ui_scene = preload("res://Scene/UI/settings_ui.tscn")
var settings_ui_instance: CanvasLayer = null

## 背包页面UI预加载
@onready var inventory_page_ui_scene = preload("res://Scene/UI/inventory_page_ui.tscn")
var inventory_page_ui_instance: CanvasLayer = null

## 提前加载淡入淡出层（你要保证 FadeLayer.tscn 路径正确）
@onready var fade_layer = preload("res://Scene/UI/fade_layer.tscn").instantiate()

## 独立的shatter效果实例（按需创建）
var shatter_effect: CanvasLayer = null

func _ready():
	_logger("Scene Manager Ready !")
	get_tree().get_root().call_deferred("add_child", fade_layer)
	# 初始状态设为透明，不阻挡输入
	fade_layer.get_node("ColorRect").visible = false

	# 连接输入处理器的暂停菜单信号
	if InputManager:
		InputManager.pause_menu_toggle_requested.connect(_on_pause_menu_toggle_requested)

# ================================
# 辅助函数
# ================================

## 验证并加载场景资源
func _load_scene_resource(path: String) -> PackedScene:
	var resource = load(path)
	if resource == null:
		push_error("Failed to load scene: %s" % path)
		return null
	if not resource is PackedScene:
		push_error("Loaded resource is not a scene: %s" % path)
		return null
	return resource

## 异步验证并加载场景资源
func _load_scene_resource_async(path: String) -> PackedScene:
	# 开始异步加载
	ResourceLoader.load_threaded_request(path)
	# 等待加载完成
	var load_status = ResourceLoader.THREAD_LOAD_IN_PROGRESS
	var resource = null
	while load_status == ResourceLoader.THREAD_LOAD_IN_PROGRESS:
		await get_tree().process_frame
		load_status = ResourceLoader.load_threaded_get_status(path)
	
	if load_status == ResourceLoader.THREAD_LOAD_LOADED:
		resource = ResourceLoader.load_threaded_get(path)
	else:
		push_error("Failed to load scene asynchronously: %s" % path)
		return null
	
	if resource == null or not resource is PackedScene:
		push_error("Loaded resource is not a scene: %s" % path)
		return null
	
	return resource

## 清理当前场景
func _cleanup_current_scene():
	if current_scene:
		current_scene.queue_free()
		await current_scene.tree_exited

## 切换到新场景（核心逻辑）
func _switch_to_scene(new_scene: Node):
	var old_scene_path: String = ""
	var old_scene_name: String = "[None]"
	if current_scene != null:
		old_scene_name = current_scene.name
		old_scene_path = current_scene.scene_file_path if current_scene.scene_file_path else ""
	
	get_tree().get_root().add_child(new_scene)
	current_scene = new_scene
	
	var new_scene_path = new_scene.scene_file_path if new_scene.scene_file_path else ""
	
	_logger("Scene changed: %s --> %s" % [old_scene_name, new_scene.name])
	
	# 发送场景切换完成信号
	scene_changed.emit(old_scene_path, new_scene_path)

# ================================
# 公共场景切换接口
# ================================

## 最基本的切换（强烈建议只用本 SceneManager 来切场景，不要到处乱用 change_scene）
func change_scene(path: String) -> void:
	var resource = _load_scene_resource(path)
	if resource == null:
		return
	
	await _cleanup_current_scene()
	var new_scene = resource.instantiate()
	_switch_to_scene(new_scene)

## 支持淡入淡出切换
func fade_to_scene(path: String) -> void:
	await fade_layer.fade_out()
	
	var resource = _load_scene_resource(path)
	if resource == null:
		await fade_layer.fade_in()
		return
	
	await _cleanup_current_scene()
	var new_scene = resource.instantiate()
	_switch_to_scene(new_scene)
	
	await fade_layer.fade_in()

## 支持异步加载大场景（防止卡顿）
func fade_to_scene_async(path: String) -> void:
	await fade_layer.fade_out()
	
	var resource = await _load_scene_resource_async(path)
	if resource == null:
		await fade_layer.fade_in()
		return
	
	await _cleanup_current_scene()
	var new_scene = resource.instantiate()
	_switch_to_scene(new_scene)
	
	await fade_layer.fade_in()

## 带shatter效果的场景切换（独立功能）
func shatter_to_scene(path: String, shatter_duration: float = 1.0) -> void:
	# 1. ───────────────────── 创建/准备层级 ─────────────────────
	if not shatter_effect:
		shatter_effect = CanvasLayer.new()
		shatter_effect.layer = 200        # 确保最顶
		shatter_effect.set_script(preload("res://Script/UI/shatter_effect.gd"))
		get_tree().root.add_child(shatter_effect)

	# fade_layer 必须在 shatter_effect 下方，但仍高于一切 UI
	fade_layer.layer = 150               # 比大多数 UI 都高
	# fade_layer.raise()                   # 再保险一次

	# 2. ───────────────────── 开黑幕 ─────────────────────
	var cr := fade_layer.get_node("ColorRect")
	cr.visible = true
	cr.color    = Color.BLACK            # RGB & α = 1
	cr.modulate = Color(1, 1, 1, 1)      # 复位透明度

	# 3. ───────────────────── 播放碎屏 ─────────────────────
	shatter_effect.start_effect(shatter_duration)

	# 3.1 立刻把旧场景隐藏（截图已完成）
	if current_scene:
		current_scene.visible = false     # 或者 current_scene.modulate.a = 0

	await get_tree().create_timer(shatter_duration).timeout

	# 4. ───────────────────── 切场景 ─────────────────────
	shatter_effect.queue_free()          # Tween 的 queue_free 再执行一次也无妨
	shatter_effect = null

	var resource := _load_scene_resource(path)
	if resource == null:
		await fade_layer.fade_in()        # 出错时照旧淡入
		return

	await _cleanup_current_scene()
	_switch_to_scene(resource.instantiate())

	# 5. ───────────────────── 淡入新场景 ─────────────────────
	await fade_layer.fade_in()
	# 此时 fade_layer 会把 modulate.a 动回 0 并隐藏

## 重新加载当前场景
func reload_current_scene() -> void:
	if current_scene:
		fade_to_scene(current_scene.scene_file_path)

# ================================
# 便捷方法
# ================================

## 返回主菜单（举例）
func go_to_main_menu():
	# 检查当前是否已经在主菜单场景，避免重复加载
	if current_scene and current_scene.scene_file_path == MAIN_MENU_SCENE:
		_logger("Already in main menu scene, skipping reload")
		return
	fade_to_scene(MAIN_MENU_SCENE)

## 显示设置UI（作为UI层）
func show_settings_ui():
	if settings_ui_instance != null:
		return  # 已经显示了
	
	settings_ui_instance = settings_ui_scene.instantiate()
	if settings_ui_instance:
		add_ui_layer(settings_ui_instance)
		_logger("Settings UI shown as overlay")
	else:
		_logger("Failed to create settings UI")

## 隐藏设置UI
func hide_settings_ui():
	if settings_ui_instance != null:
		remove_ui_layer(settings_ui_instance)
		settings_ui_instance.queue_free()
		settings_ui_instance = null
		_logger("Settings UI hidden")

## 显示背包页面UI
func show_inventory_ui():
	if inventory_page_ui_instance != null:
		return  # 已经显示了
	
	inventory_page_ui_instance = inventory_page_ui_scene.instantiate()
	if inventory_page_ui_instance:
		add_ui_layer(inventory_page_ui_instance)
		inventory_page_ui_instance.show_inventory_page()
		_logger("Inventory page UI shown as overlay")
	else:
		_logger("Failed to create inventory page UI")

## 隐藏背包页面UI
func hide_inventory_ui():
	if inventory_page_ui_instance != null:
		remove_ui_layer(inventory_page_ui_instance)
		inventory_page_ui_instance.queue_free()
		inventory_page_ui_instance = null
		_logger("Inventory page UI hidden")

func go_to_main_school():
	fade_to_scene(MAIN_SCHOOL_SCENE)

# ================================
# UI层管理
# ================================

## 添加UI层到管理
func add_ui_layer(ui_layer: CanvasLayer):
	if ui_layer not in ui_layers:
		ui_layers.append(ui_layer)
		get_tree().root.add_child(ui_layer)
		_logger("UI layer added: %s" % ui_layer.name)

## 移除UI层
func remove_ui_layer(ui_layer: CanvasLayer):
	if ui_layer in ui_layers:
		ui_layers.erase(ui_layer)
		if ui_layer.get_parent():
			ui_layer.get_parent().remove_child(ui_layer)
		_logger("UI layer removed: %s" % ui_layer.name)

## 清理所有UI层
func clear_all_ui_layers():
	for layer in ui_layers:
		if layer.get_parent():
			layer.get_parent().remove_child(layer)
	ui_layers.clear()
	_logger("All UI layers cleared")

# ================================
# 暂停菜单管理 - 统一UI管理
# ================================

## 输入处理器请求切换暂停菜单
func _on_pause_menu_toggle_requested():
	if is_pause_menu_active:
		hide_pause_menu()
	else:
		show_pause_menu()

## 显示暂停菜单
func show_pause_menu():
	if pause_menu != null:
		return  # 已经显示了

	# 创建暂停菜单实例
	pause_menu = _create_pause_menu()
	if not pause_menu:
		_logger("Failed to create pause menu")
		return

	# 添加到UI层
	add_ui_layer(pause_menu)
	is_pause_menu_active = true

	# 连接暂停菜单信号
	_connect_pause_menu_signals()

	# 通知状态管理器切换到暂停状态
	if AppStateManager:
		AppStateManager.pause_game()

	_logger("Pause menu shown")

## 隐藏暂停菜单
func hide_pause_menu():
	if pause_menu == null:
		return

	# 从UI层移除
	remove_ui_layer(pause_menu)
	pause_menu.queue_free()
	pause_menu = null
	is_pause_menu_active = false

	# 通知状态管理器恢复游戏
	if AppStateManager:
		AppStateManager.resume_game()

	_logger("Pause menu hidden")

## 创建暂停菜单实例
func _create_pause_menu() -> CanvasLayer:
	var resource = _load_scene_resource(PAUSE_MENU_SCENE)
	if resource == null:
		_logger("Failed to load pause menu scene")
		return null

	var menu = resource.instantiate()
	if not menu:
		_logger("Failed to instantiate pause menu")
		return null

	return menu

## 连接暂停菜单信号
func _connect_pause_menu_signals():
	if not pause_menu:
		return

	# 连接暂停菜单的各种信号
	if pause_menu.has_signal("resume_requested"):
		pause_menu.resume_requested.connect(_on_pause_menu_resume)
	if pause_menu.has_signal("save_requested"):
		pause_menu.save_requested.connect(_on_pause_menu_save)
	if pause_menu.has_signal("load_requested"):
		pause_menu.load_requested.connect(_on_pause_menu_load)
	if pause_menu.has_signal("settings_requested"):
		pause_menu.settings_requested.connect(_on_pause_menu_settings)
	if pause_menu.has_signal("main_menu_requested"):
		pause_menu.main_menu_requested.connect(_on_pause_menu_main_menu)
	if pause_menu.has_signal("quit_requested"):
		pause_menu.quit_requested.connect(_on_pause_menu_quit)

## 暂停菜单信号处理
func _on_pause_menu_resume():
	hide_pause_menu()

func _on_pause_menu_save():
	# 使用GameManager来确定正确的保存槽位
	if SaveManager and GameManager:
		var save_slot = GameManager.get_save_slot_for_current_game()

		var success = SaveManager.save_game(save_slot)
		if success:
			_logger("Game saved to slot %d" % save_slot)
		else:
			_logger("Failed to save game to slot %d" % save_slot)

func _on_pause_menu_load():
	# 恢复存档：只有当前已有有效存档时才能恢复
	hide_pause_menu()
	if SaveManager:
		# 检查当前是否有有效的存档可以恢复
		var current_slot = SaveManager.get_current_save_slot()
		if current_slot > 0 and SaveManager.has_save(current_slot):
			# 有有效的当前存档，直接重新加载
			var success = SaveManager.reload_current_save()
			if success:
				_logger("Game reloaded from current save slot %d" % current_slot)
			else:
				_logger("Failed to reload current save slot %d" % current_slot)
		else:
			# 没有有效的当前存档（比如新游戏未保存），无法恢复
			_logger("Cannot reload - no current save exists (unsaved new game)")

func _on_pause_menu_settings():
	# 转到设置状态，暂停菜单会在状态转换中被适当处理
	if AppStateManager:
		AppStateManager.open_settings()

func _on_pause_menu_main_menu():
	# 直接清理暂停菜单UI，不调用resume_game()
	if pause_menu != null:
		# 从UI层移除
		remove_ui_layer(pause_menu)
		pause_menu.queue_free()
		pause_menu = null
		is_pause_menu_active = false
		_logger("Pause menu cleaned up for main menu transition")

	# 停止自动保存
	if SaveManager:
		SaveManager.stop_autosave()
	# 从PAUSED状态直接返回主菜单
	if AppStateManager:
		AppStateManager.go_to_main_menu()

func _on_pause_menu_quit():
	hide_pause_menu()
	# 退出应用
	if GameManager:
		GameManager.quit_application()

## 状态转换时的暂停菜单管理（供AppStateManager调用）
func cleanup_pause_menu_for_state_change():
	if pause_menu != null:
		remove_ui_layer(pause_menu)
		pause_menu.queue_free()
		pause_menu = null
		is_pause_menu_active = false
		_logger("Pause menu cleaned up for state change")

func show_pause_menu_for_state_change():
	if pause_menu == null:
		pause_menu = _create_pause_menu()
		if pause_menu:
			add_ui_layer(pause_menu)
			_connect_pause_menu_signals()
			is_pause_menu_active = true
			_logger("Pause menu shown for state change")
		else:
			_logger("Failed to create pause menu for state change")

func _logger(msg: String) -> void:
	print("[SceneManager] %s" % msg)
