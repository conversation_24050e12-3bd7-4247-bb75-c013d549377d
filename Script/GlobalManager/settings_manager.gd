extends Node

## 设置应用器 - 专门负责将设置应用到游戏系统
## 从 GameManager 中提取的设置应用逻辑，实现单一职责原则

## 控制动作列表 - 需要处理InputMap的设置项
const CONTROL_ACTIONS = [
	"move_up",
	"move_down",
	"move_left",
	"move_right",
	"action",
	"menu"
]

## 音频设置映射
const AUDIO_BUS_MAP = {
	"master_volume": 0,  # Master bus
	"music_volume": 1,   # Music bus (需要在项目中创建)
	"sfx_volume": 2      # SFX bus (需要在项目中创建)
}

func _ready():
	_logger("Settings Manager Ready!")
	_setup_settings_listeners()

## 设置监听器
func _setup_settings_listeners():
	# 监听SaveManager的设置改变信号
	if SaveManager:
		SaveManager.settings_changed.connect(_on_settings_changed)
		_logger("Settings listeners setup complete")

## 应用初始设置
func apply_initial_settings():
	_apply_all_control_settings()
	_apply_all_audio_settings()
	_apply_all_display_settings()
	_logger("Initial settings applied")

# ================================
# 设置改变处理
# ================================

## 处理设置改变
func _on_settings_changed(key: String, value: Variant):
	_logger("Setting changed: %s = %s" % [key, str(value)])
	
	# 根据设置类型分发处理
	if key in CONTROL_ACTIONS:
		_update_input_map(key, value)
	elif key in AUDIO_BUS_MAP:
		_apply_audio_setting(key, value)
	elif key in ["fullscreen", "vsync"]:
		_apply_display_setting(key, value)
	elif key == "language":
		_apply_language_setting(value)

# ================================
# 控制设置处理 (InputMap)
# ================================

## 应用所有控制设置
func _apply_all_control_settings():
	if not SaveManager:
		_logger("SaveManager not available")
		return
		
	var controls = SaveManager.get_settings_section("controls")
	if controls.is_empty():
		_logger("No control settings found, using defaults")
		return
	
	for action in controls:
		if action in CONTROL_ACTIONS:
			_update_input_map(action, controls[action])
	
	_logger("All control settings applied")

## 更新InputMap中的单个动作
func _update_input_map(action: String, key_string: String):
	if not InputMap.has_action(action):
		_logger("Warning: InputMap action not found: %s" % action)
		return
	
	# 检查当前绑定是否已经是目标按键
	var current_events = InputMap.action_get_events(action)
	var target_event = _parse_key_string(key_string)
	
	if not target_event:
		_logger("Warning: Invalid key string: %s for action: %s" % [key_string, action])
		return
	
	# 检查是否需要更新
	var needs_update = true
	if current_events.size() == 1 and current_events[0] is InputEventKey:
		var current_key = current_events[0] as InputEventKey
		
		# 比较逻辑：优先比较physical_keycode，如果没有则比较keycode
		var current_code = current_key.physical_keycode if current_key.physical_keycode != 0 else current_key.keycode
		var target_code = target_event.physical_keycode if target_event.physical_keycode != 0 else target_event.keycode
		
		if current_code == target_code and current_code != 0:
			needs_update = false
	
	# 只在需要更新时才执行更新和打印日志
	if needs_update:
		InputMap.action_erase_events(action)
		InputMap.action_add_event(action, target_event)
		_logger("InputMap updated: %s -> %s" % [action, key_string])
	# 如果不需要更新，则静默跳过

## 解析按键字符串为InputEvent
func _parse_key_string(key_string: String) -> InputEvent:
	if key_string.is_empty():
		return null
	
	var event = InputEventKey.new()
	
	# 处理特殊按键 - 这些使用keycode
	match key_string.to_lower():
		"space":
			event.keycode = KEY_SPACE
		"escape", "esc":
			event.keycode = KEY_ESCAPE
		"enter", "return":
			event.keycode = KEY_ENTER
		"tab":
			event.keycode = KEY_TAB
		"shift":
			event.keycode = KEY_SHIFT
		"ctrl", "control":
			event.keycode = KEY_CTRL
		"alt":
			event.keycode = KEY_ALT
		"up":
			event.keycode = KEY_UP
		"down":
			event.keycode = KEY_DOWN
		"left":
			event.keycode = KEY_LEFT
		"right":
			event.keycode = KEY_RIGHT
		_:
			# 对于字母和数字，优先使用physical_keycode匹配Godot默认行为
			if key_string.length() == 1:
				var char_code = key_string.to_upper().unicode_at(0)
				# 设置physical_keycode而不是keycode，匹配project.godot中的格式
				event.physical_keycode = char_code
				event.keycode = 0  # 明确设置为0
			else:
				# 尝试从字符串获取keycode
				var keycode = OS.find_keycode_from_string(key_string)
				if keycode != KEY_NONE:
					event.keycode = keycode
				else:
					return null
	
	return event

# ================================
# 音频设置处理
# ================================

## 应用所有音频设置
func _apply_all_audio_settings():
	if not SaveManager:
		_logger("SaveManager not available")
		return
		
	for setting in AUDIO_BUS_MAP:
		var value = SaveManager.get_setting(setting)
		if value != null:
			_apply_audio_setting(setting, value)
	
	_logger("All audio settings applied")

## 应用单个音频设置
func _apply_audio_setting(setting: String, value: float):
	if not setting in AUDIO_BUS_MAP:
		_logger("Warning: Unknown audio setting: %s" % setting)
		return
	
	var bus_index = AUDIO_BUS_MAP[setting]
	
	# 检查音频总线是否存在
	if bus_index >= AudioServer.bus_count:
		_logger("Warning: Audio bus %d does not exist for setting: %s" % [bus_index, setting])
		return
	
	# 将线性音量转换为分贝并应用
	var db_volume = linear_to_db(clamp(value, 0.0, 1.0))
	AudioServer.set_bus_volume_db(bus_index, db_volume)
	
	_logger("Audio setting applied: %s = %.2f (%.1f dB)" % [setting, value, db_volume])

# ================================
# 显示设置处理
# ================================

## 应用所有显示设置
func _apply_all_display_settings():
	if not SaveManager:
		_logger("SaveManager not available")
		return
		
	var fullscreen = SaveManager.get_setting("fullscreen")
	if fullscreen != null:
		_apply_display_setting("fullscreen", fullscreen)
	
	var vsync = SaveManager.get_setting("vsync")
	if vsync != null:
		_apply_display_setting("vsync", vsync)

	var window_mode = SaveManager.get_setting("window_mode", "display")
	if window_mode != null:
		_apply_display_setting("window_mode", window_mode)
	
	_logger("All display settings applied")

## 应用单个显示设置
func _apply_display_setting(setting: String, value: Variant):
	match setting:
		"fullscreen":
			if value:
				DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
				_logger("Fullscreen enabled")
			else:
				DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_WINDOWED)
				_logger("Fullscreen disabled")
		
		"vsync":
			if value:
				DisplayServer.window_set_vsync_mode(DisplayServer.VSYNC_ENABLED)
				_logger("VSync enabled")
			else:
				DisplayServer.window_set_vsync_mode(DisplayServer.VSYNC_DISABLED)
				_logger("VSync disabled")
		"window_mode":
			DisplayServer.window_set_mode(value)
			_logger("Window mode set to: %s" % value)
		_:
			_logger("Warning: Unknown display setting: %s" % setting)

# ================================
# 其他设置处理
# ================================

## 应用语言设置
func _apply_language_setting(language: String):
	TranslationServer.set_locale(language)
	_logger("Language changed to: %s" % language)

# ================================
# 调试和工具方法
# ================================

## 获取当前InputMap配置 (调试用)
func get_current_input_map() -> Dictionary:
	var input_map = {}
	for action in CONTROL_ACTIONS:
		if InputMap.has_action(action):
			var events = InputMap.action_get_events(action)
			if events.size() > 0 and events[0] is InputEventKey:
				var key_event = events[0] as InputEventKey
				input_map[action] = OS.get_keycode_string(key_event.keycode)
		else:
			input_map[action] = "Not found"
	return input_map

## 重置InputMap到默认值
func reset_input_map_to_defaults():
	_logger("Resetting InputMap to defaults...")
	
	# 这里可以设置默认的按键绑定
	var defaults = {
		"move_up": "W",
		"move_down": "S", 
		"move_left": "A",
		"move_right": "D",
		"action": "F",
		"menu": "Escape"
	}
	
	for action in defaults:
		_update_input_map(action, defaults[action])
	
	_logger("InputMap reset complete")

## 统一日志输出函数
func _logger(msg: String) -> void:
	print("[SettingsManager] %s" % msg)
