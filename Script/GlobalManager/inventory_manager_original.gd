extends Node

## 背包管理器 - 管理物品和装备系统
## 作为autoload运行，提供全局背包功能

# ================================
# 物品类型定义
# ================================

enum ItemType {
	CONSUMABLE,    # 消耗品
	EQUIPMENT,     # 装备
	MATERIAL,      # 材料
	KEY_ITEM,      # 关键物品
	MISC          # 杂项
}

enum EquipmentSlot {
	WEAPON,        # 武器
	ARMOR,         # 护甲
	ACCESSORY,     # 饰品
	SHOES,         # 鞋子
	HELMET        # 头盔
}

# ================================
# 物品基础类
# ================================

class Item:
	var id: String
	var name: String
	var description: String
	var icon_path: String
	var type: ItemType
	var stack_size: int = 1
	var value: int = 0
	var rarity: int = 0  # 0=普通, 1=稀有, 2=史诗, 3=传说
	
	func _init(item_id: String = "", item_name: String = "", item_desc: String = ""):
		id = item_id
		name = item_name
		description = item_desc
	
	func to_dict() -> Dictionary:
		return {
			"id": id,
			"name": name,
			"description": description,
			"icon_path": icon_path,
			"type": type,
			"stack_size": stack_size,
			"value": value,
			"rarity": rarity
		}
	
	func from_dict(data: Dictionary):
		id = data.get("id", "")
		name = data.get("name", "")
		description = data.get("description", "")
		icon_path = data.get("icon_path", "")
		type = data.get("type", ItemType.MISC)
		stack_size = data.get("stack_size", 1)
		value = data.get("value", 0)
		rarity = data.get("rarity", 0)

# ================================
# 装备类
# ================================

class Equipment extends Item:
	var slot: EquipmentSlot
	var stats: Dictionary = {}  # 属性加成 {"hp": 10, "attack": 5}
	var level_requirement: int = 1
	
	func _init(item_id: String = "", item_name: String = "", item_desc: String = ""):
		super(item_id, item_name, item_desc)
		type = ItemType.EQUIPMENT
	
	func to_dict() -> Dictionary:
		var base_dict = super.to_dict()
		base_dict["slot"] = slot
		base_dict["stats"] = stats
		base_dict["level_requirement"] = level_requirement
		return base_dict
	
	func from_dict(data: Dictionary):
		super.from_dict(data)
		slot = data.get("slot", EquipmentSlot.WEAPON)
		stats = data.get("stats", {})
		level_requirement = data.get("level_requirement", 1)

# ================================
# 背包物品槽位
# ================================

class InventorySlot:
	var item: Item = null
	var quantity: int = 0
	
	func _init(slot_item: Item = null, slot_quantity: int = 0):
		item = slot_item
		quantity = slot_quantity
	
	func is_empty() -> bool:
		return item == null or quantity <= 0
	
	func can_stack_with(other_item: Item) -> bool:
		if is_empty() or other_item == null:
			return true
		return item.id == other_item.id and quantity < item.stack_size
	
	func add_item(new_item: Item, amount: int) -> int:
		if is_empty():
			item = new_item
			quantity = min(amount, new_item.stack_size)
			return amount - quantity
		elif can_stack_with(new_item):
			var space = item.stack_size - quantity
			var add_amount = min(amount, space)
			quantity += add_amount
			return amount - add_amount
		return amount
	
	func remove_item(amount: int) -> int:
		if is_empty():
			return 0
		var removed = min(amount, quantity)
		quantity -= removed
		if quantity <= 0:
			item = null
			quantity = 0
		return removed
	
	func to_dict() -> Dictionary:
		return {
			"item": item.to_dict() if item else {},
			"quantity": quantity
		}
	
	func from_dict(data: Dictionary):
		quantity = data.get("quantity", 0)
		var item_data = data.get("item", {})
		if item_data.size() > 0:
			if item_data.get("type") == ItemType.EQUIPMENT:
				item = Equipment.new()
			else:
				item = Item.new()
			item.from_dict(item_data)
		else:
			item = null

# ================================
# 背包系统常量
# ================================

const DEFAULT_INVENTORY_SIZE = 30
const MAX_INVENTORY_SIZE = 100

# ================================
# 背包数据
# ================================

var inventory_slots: Array[InventorySlot] = []
var equipped_items: Dictionary = {}  # EquipmentSlot -> Equipment
var inventory_size: int = DEFAULT_INVENTORY_SIZE

# ================================
# 物品数据库
# ================================

var item_database: Dictionary = {}

# ================================
# 信号定义
# ================================

signal inventory_changed()
signal item_added(item: Item, quantity: int)
signal item_removed(item: Item, quantity: int)
signal item_used(item: Item)
signal equipment_changed(slot: EquipmentSlot, old_equipment: Equipment, new_equipment: Equipment)

func _ready():
	_logger("Inventory Manager Ready!")
	_initialize_inventory()
	_setup_item_database()

# ================================
# 初始化
# ================================

func _initialize_inventory():
	inventory_slots.clear()
	for i in range(inventory_size):
		inventory_slots.append(InventorySlot.new())
	
	# 初始化装备槽位
	for slot in EquipmentSlot.values():
		equipped_items[slot] = null
	
	_logger("Inventory initialized with %d slots" % inventory_size)

func _setup_item_database():
	# 创建基础消耗品
	_create_consumable_item("potion_health", "生命药水", "恢复50点生命值", 50, 1)
	_create_consumable_item("potion_mana", "魔法药水", "恢复30点魔法值", 30, 1)
	_create_consumable_item("bread", "面包", "恢复少量生命值", 10, 10)
	
	# 创建基础装备
	_create_equipment_item("sword_iron", "铁剑", "基础的铁制剑", EquipmentSlot.WEAPON, {"attack": 10}, 1, 100, 1)
	_create_equipment_item("armor_leather", "皮甲", "基础的皮制护甲", EquipmentSlot.ARMOR, {"defense": 5, "hp": 20}, 1, 80, 0)
	_create_equipment_item("boots_leather", "皮靴", "基础的皮制靴子", EquipmentSlot.SHOES, {"speed": 2, "defense": 2}, 1, 40, 0)
	
	# 创建材料
	_create_material_item("iron_ore", "铁矿石", "用于锻造的基础材料", 10, 50)
	_create_material_item("leather", "皮革", "用于制作装备的材料", 5, 20)
	
	# 创建关键物品
	_create_key_item("school_key", "学校钥匙", "打开学校大门的钥匙")
	
	_logger("Item database initialized with %d items" % item_database.size())

# ================================
# 物品数据库辅助函数
# ================================

func _create_consumable_item(id: String, item_name: String, desc: String, value: int, stack_size: int):
	var item = Item.new(id, item_name, desc)
	item.type = ItemType.CONSUMABLE
	item.value = value
	item.stack_size = stack_size
	item.icon_path = "res://Asset/Icon/consumable_%s.png" % id
	item_database[id] = item

func _create_equipment_item(id: String, item_name: String, desc: String, slot: EquipmentSlot, stats: Dictionary, level_req: int, value: int, rarity: int):
	var equipment = Equipment.new(id, item_name, desc)
	equipment.slot = slot
	equipment.stats = stats
	equipment.level_requirement = level_req
	equipment.value = value
	equipment.rarity = rarity
	equipment.icon_path = "res://Asset/Icon/equipment_%s.png" % id
	item_database[id] = equipment

func _create_material_item(id: String, item_name: String, desc: String, value: int, stack_size: int):
	var item = Item.new(id, item_name, desc)
	item.type = ItemType.MATERIAL
	item.value = value
	item.stack_size = stack_size
	item.icon_path = "res://Asset/Icon/material_%s.png" % id
	item_database[id] = item

func _create_key_item(id: String, item_name: String, desc: String):
	var item = Item.new(id, item_name, desc)
	item.type = ItemType.KEY_ITEM
	item.stack_size = 1
	item.icon_path = "res://Asset/Icon/key_%s.png" % id
	item_database[id] = item

# ================================
# 背包操作
# ================================

func add_item(item_id: String, quantity: int = 1) -> bool:
	var item = get_item_from_database(item_id)
	if not item:
		_logger("Item not found in database: %s" % item_id)
		return false
	
	var remaining = quantity
	
	# 先尝试堆叠到现有物品
	for slot in inventory_slots:
		if not slot.is_empty() and slot.can_stack_with(item):
			remaining = slot.add_item(item, remaining)
			if remaining <= 0:
				break
	
	# 如果还有剩余，寻找空槽位
	if remaining > 0:
		for slot in inventory_slots:
			if slot.is_empty():
				remaining = slot.add_item(item, remaining)
				if remaining <= 0:
					break
	
	var added_quantity = quantity - remaining
	if added_quantity > 0:
		item_added.emit(item, added_quantity)
		inventory_changed.emit()
		_logger("Added %d x %s to inventory" % [added_quantity, item.name])
	
	if remaining > 0:
		_logger("Inventory full, could not add %d x %s" % [remaining, item.name])
	
	return remaining == 0

func remove_item(item_id: String, quantity: int = 1) -> int:
	var removed_total = 0
	var remaining = quantity
	
	for slot in inventory_slots:
		if not slot.is_empty() and slot.item.id == item_id:
			var removed = slot.remove_item(remaining)
			removed_total += removed
			remaining -= removed
			if remaining <= 0:
				break
	
	if removed_total > 0:
		var item = get_item_from_database(item_id)
		if item:
			item_removed.emit(item, removed_total)
		inventory_changed.emit()
		_logger("Removed %d x %s from inventory" % [removed_total, item_id])
	
	return removed_total

## 按槽位索引移除物品
func remove_item_from_slot(slot_index: int, quantity: int = 1) -> int:
	if slot_index < 0 or slot_index >= inventory_slots.size():
		_logger("Invalid slot index: %d" % slot_index)
		return 0
	
	var slot = inventory_slots[slot_index]
	if slot.is_empty():
		return 0
	
	var item = slot.item
	var removed = slot.remove_item(quantity)
	
	if removed > 0:
		item_removed.emit(item, removed)
		inventory_changed.emit()
		_logger("Removed %d x %s from slot %d" % [removed, item.name, slot_index])
	
	return removed

func has_item(item_id: String, required_quantity: int = 1) -> bool:
	var total_quantity = get_item_quantity(item_id)
	return total_quantity >= required_quantity

func get_item_quantity(item_id: String) -> int:
	var total = 0
	for slot in inventory_slots:
		if not slot.is_empty() and slot.item.id == item_id:
			total += slot.quantity
	return total

func use_item(slot_index: int) -> bool:
	if slot_index < 0 or slot_index >= inventory_slots.size():
		return false
	
	var slot = inventory_slots[slot_index]
	if slot.is_empty():
		return false
	
	var item = slot.item
	
	# 处理不同类型物品的使用
	match item.type:
		ItemType.CONSUMABLE:
			_use_consumable_item(item)
			slot.remove_item(1)
			item_used.emit(item)
			inventory_changed.emit()
			return true
		ItemType.EQUIPMENT:
			return equip_item(slot_index)
		_:
			_logger("Cannot use item: %s" % item.name)
			return false

func _use_consumable_item(item: Item):
	# 应用消耗品效果到玩家
	match item.id:
		"potion_health":
			if GameManager:
				var current_hp = GameManager.player_data.get("hp", 100)
				var max_hp = GameManager.player_data.get("max_hp", 100)
				var new_hp = min(current_hp + 50, max_hp)
				GameManager.update_player_data("hp", new_hp)
				_logger("Used health potion, HP: %d -> %d" % [current_hp, new_hp])
		"potion_mana":
			# 类似处理魔法值
			_logger("Used mana potion")
		"bread":
			if GameManager:
				var current_hp = GameManager.player_data.get("hp", 100)
				var max_hp = GameManager.player_data.get("max_hp", 100)
				var new_hp = min(current_hp + 10, max_hp)
				GameManager.update_player_data("hp", new_hp)
				_logger("Used bread, HP: %d -> %d" % [current_hp, new_hp])

# ================================
# 装备系统
# ================================

func equip_item(slot_index: int) -> bool:
	if slot_index < 0 or slot_index >= inventory_slots.size():
		return false
	
	var slot = inventory_slots[slot_index]
	if slot.is_empty() or slot.item.type != ItemType.EQUIPMENT:
		return false
	
	var equipment = slot.item as Equipment
	var equipment_slot = equipment.slot
	
	# 检查等级需求
	if GameManager:
		var player_level = GameManager.player_data.get("level", 1)
		if player_level < equipment.level_requirement:
			_logger("Level too low to equip %s (requires level %d)" % [equipment.name, equipment.level_requirement])
			return false
	
	# 卸下当前装备
	var old_equipment = equipped_items.get(equipment_slot)
	if old_equipment:
		# 将旧装备放回背包
		if not add_item(old_equipment.id, 1):
			_logger("Cannot unequip: inventory full")
			return false
	
	# 装备新物品
	equipped_items[equipment_slot] = equipment
	slot.remove_item(1)
	
	# 应用装备属性
	_apply_equipment_stats(equipment, true)
	
	equipment_changed.emit(equipment_slot, old_equipment, equipment)
	inventory_changed.emit()
	
	_logger("Equipped %s in slot %s" % [equipment.name, EquipmentSlot.keys()[equipment_slot]])
	return true

func unequip_item(equipment_slot: EquipmentSlot) -> bool:
	var equipment = equipped_items.get(equipment_slot)
	if not equipment:
		return false
	
	# 检查背包空间
	if not add_item(equipment.id, 1):
		_logger("Cannot unequip: inventory full")
		return false
	
	# 移除装备属性
	_apply_equipment_stats(equipment, false)
	
	var old_equipment = equipped_items[equipment_slot]
	equipped_items[equipment_slot] = null
	
	equipment_changed.emit(equipment_slot, old_equipment, null)
	
	_logger("Unequipped %s from slot %s" % [equipment.name, EquipmentSlot.keys()[equipment_slot]])
	return true

func _apply_equipment_stats(equipment: Equipment, apply: bool):
	if not GameManager:
		return
	
	var multiplier = 1 if apply else -1
	
	for stat_name in equipment.stats:
		var stat_value = equipment.stats[stat_name] * multiplier
		var current_value = GameManager.player_data.get(stat_name, 0)
		GameManager.update_player_data(stat_name, current_value + stat_value)

# ================================
# 查询函数
# ================================

func get_item_from_database(item_id: String) -> Item:
	return item_database.get(item_id)

func get_inventory_slots() -> Array[InventorySlot]:
	return inventory_slots

func get_equipped_items() -> Dictionary:
	return equipped_items

func get_equipped_item(slot: EquipmentSlot) -> Equipment:
	return equipped_items.get(slot)

func is_inventory_full() -> bool:
	for slot in inventory_slots:
		if slot.is_empty():
			return false
	return true

func get_empty_slot_count() -> int:
	var count = 0
	for slot in inventory_slots:
		if slot.is_empty():
			count += 1
	return count

# ================================
# 存档系统集成
# ================================

func get_inventory_data() -> Dictionary:
	var data = {
		"inventory_size": inventory_size,
		"slots": [],
		"equipped_items": {}
	}
	
	# 保存背包槽位
	for slot in inventory_slots:
		data.slots.append(slot.to_dict())
	
	# 保存装备
	for slot in equipped_items:
		if equipped_items[slot]:
			data.equipped_items[str(slot)] = equipped_items[slot].to_dict()
	
	return data

func set_inventory_data(data: Dictionary):
	if not data:
		return
	
	inventory_size = data.get("inventory_size", DEFAULT_INVENTORY_SIZE)
	
	# 恢复背包槽位
	inventory_slots.clear()
	var slots_data = data.get("slots", [])
	for i in range(inventory_size):
		var slot = InventorySlot.new()
		if i < slots_data.size():
			slot.from_dict(slots_data[i])
		inventory_slots.append(slot)
	
	# 恢复装备
	equipped_items.clear()
	for slot in EquipmentSlot.values():
		equipped_items[slot] = null
	
	var equipped_data = data.get("equipped_items", {})
	for slot_str in equipped_data:
		var slot_index = int(slot_str)
		var equipment_data = equipped_data[slot_str]
		var equipment = Equipment.new()
		equipment.from_dict(equipment_data)
		equipped_items[slot_index] = equipment
	
	inventory_changed.emit()
	_logger("Inventory data loaded successfully")

# ================================
# 工具函数
# ================================

func _logger(msg: String) -> void:
	print("[InventoryManager] %s" % msg)

# ================================
# 背包整理和物品移动功能
# ================================

## 移动物品到指定槽位
func move_item(from_slot: int, to_slot: int) -> bool:
	if from_slot < 0 or from_slot >= inventory_slots.size():
		_logger("Invalid from_slot: %d" % from_slot)
		return false
	if to_slot < 0 or to_slot >= inventory_slots.size():
		_logger("Invalid to_slot: %d" % to_slot)
		return false
	if from_slot == to_slot:
		return true  # 移动到自己，什么都不做
	
	var from_slot_obj = inventory_slots[from_slot]
	var to_slot_obj = inventory_slots[to_slot]
	
	if from_slot_obj.is_empty():
		_logger("Source slot is empty: %d" % from_slot)
		return false
	
	# 如果目标槽位为空，直接移动
	if to_slot_obj.is_empty():
		to_slot_obj.item = from_slot_obj.item
		to_slot_obj.quantity = from_slot_obj.quantity
		from_slot_obj.item = null
		from_slot_obj.quantity = 0
		
		inventory_changed.emit()
		_logger("Moved item from slot %d to slot %d" % [from_slot, to_slot])
		return true
	
	# 如果目标槽位有物品，尝试堆叠或交换
	if from_slot_obj.item.id == to_slot_obj.item.id and to_slot_obj.quantity < to_slot_obj.item.stack_size:
		# 尝试堆叠
		var space = to_slot_obj.item.stack_size - to_slot_obj.quantity
		var move_amount = min(from_slot_obj.quantity, space)
		
		to_slot_obj.quantity += move_amount
		from_slot_obj.quantity -= move_amount
		
		if from_slot_obj.quantity <= 0:
			from_slot_obj.item = null
			from_slot_obj.quantity = 0
		
		inventory_changed.emit()
		_logger("Stacked %d items from slot %d to slot %d" % [move_amount, from_slot, to_slot])
		return true
	else:
		# 交换物品
		return swap_items(from_slot, to_slot)

## 交换两个槽位的物品
func swap_items(slot1: int, slot2: int) -> bool:
	if slot1 < 0 or slot1 >= inventory_slots.size():
		_logger("Invalid slot1: %d" % slot1)
		return false
	if slot2 < 0 or slot2 >= inventory_slots.size():
		_logger("Invalid slot2: %d" % slot2)
		return false
	if slot1 == slot2:
		return true  # 交换自己，什么都不做
	
	var temp_item = inventory_slots[slot1].item
	var temp_quantity = inventory_slots[slot1].quantity
	
	inventory_slots[slot1].item = inventory_slots[slot2].item
	inventory_slots[slot1].quantity = inventory_slots[slot2].quantity
	
	inventory_slots[slot2].item = temp_item
	inventory_slots[slot2].quantity = temp_quantity
	
	inventory_changed.emit()
	_logger("Swapped items between slot %d and slot %d" % [slot1, slot2])
	return true

## 一键整理背包
func organize_inventory():
	# 收集所有非空物品
	var items_to_sort: Array[Dictionary] = []
	
	for i in range(inventory_slots.size()):
		var slot = inventory_slots[i]
		if not slot.is_empty():
			items_to_sort.append({
				"item": slot.item,
				"quantity": slot.quantity,
				"original_slot": i
			})
	
	# 清空所有槽位
	for slot in inventory_slots:
		slot.item = null
		slot.quantity = 0
	
	# 按物品类型和ID排序
	items_to_sort.sort_custom(_compare_items_for_sorting)
	
	# 重新放置物品，优先堆叠
	var current_slot = 0
	
	for item_data in items_to_sort:
		var item = item_data.item
		var remaining_quantity = item_data.quantity
		
		# 首先尝试与已有的相同物品堆叠
		var stacked = false
		for i in range(current_slot):
			var slot = inventory_slots[i]
			if not slot.is_empty() and slot.item.id == item.id and slot.quantity < item.stack_size:
				var space = item.stack_size - slot.quantity
				var stack_amount = min(remaining_quantity, space)
				slot.quantity += stack_amount
				remaining_quantity -= stack_amount
				stacked = true
				if remaining_quantity <= 0:
					break
		
		# 如果还有剩余，放到新槽位
		while remaining_quantity > 0 and current_slot < inventory_slots.size():
			var slot = inventory_slots[current_slot]
			var place_amount = min(remaining_quantity, item.stack_size)
			slot.item = item
			slot.quantity = place_amount
			remaining_quantity -= place_amount
			current_slot += 1
	
	inventory_changed.emit()
	_logger("Inventory organized")

## 物品排序比较函数
func _compare_items_for_sorting(a: Dictionary, b: Dictionary) -> bool:
	var item_a = a.item as Item
	var item_b = b.item as Item
	
	# 首先按类型排序
	if item_a.type != item_b.type:
		return item_a.type < item_b.type
	
	# 然后按稀有度排序（高稀有度在前）
	if item_a.rarity != item_b.rarity:
		return item_a.rarity > item_b.rarity
	
	# 最后按物品ID排序
	return item_a.id < item_b.id

# ================================
# 调试和测试函数
# ================================
func clear_inventory():
	_initialize_inventory()
	_logger("Inventory cleared")


func add_test_items():
	add_item("potion_health", 5)
	add_item("bread", 10)
	add_item("sword_iron", 1)
	add_item("armor_leather", 1)
	add_item("iron_ore", 20)
	_logger("Added test items to inventory")

func print_inventory():
	_logger("=== Inventory Contents ===")
	for i in range(inventory_slots.size()):
		var slot = inventory_slots[i]
		if not slot.is_empty():
			_logger("Slot %d: %s x%d" % [i, slot.item.name, slot.quantity])
	
	_logger("=== Equipped Items ===")
	for slot in equipped_items:
		var equipment = equipped_items[slot]
		if equipment:
			_logger("%s: %s" % [EquipmentSlot.keys()[slot], equipment.name])
