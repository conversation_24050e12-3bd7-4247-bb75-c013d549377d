extends Node

## 游戏管理器 - 负责协调各个管理器和游戏流程控制
## 作为autoload运行，处理跨系统协调
## 重构后使用新的服务架构：StatService + ItemEffectService + TagUtil

## 服务层引用
var stat_service: StatService
var item_effect_service: ItemEffectService

## HUD界面
var player_hud: CanvasLayer = null

## 新游戏的预期槽位（在第一次保存前使用）
var intended_save_slot: int = -1

func _ready():
	_logger("Game Manager (Refactored) Ready!")
	_setup_service_architecture()
	_setup_manager_coordination()
	_connect_state_manager_signals()
	# PlayerHUD由状态机控制，不在此创建

# ================================
# 服务架构初始化
# ================================

## 设置新的服务架构
func _setup_service_architecture():
	# 创建StatService实例
	stat_service = preload("res://Script/GameService/stat_service.gd").new()
	stat_service.name = "StatService"
	add_child(stat_service)
	
	# 创建ItemEffectService实例
	item_effect_service = preload("res://Script/GameService/item_effect_service.gd").new()
	item_effect_service.name = "ItemEffectService"
	add_child(item_effect_service)
	
	# 连接StatService信号用于HUD更新
	if stat_service:
		stat_service.stats_changed.connect(_on_stats_changed)
		_logger("Connected to StatService signals")
	
	# 确保ItemDatabase加载
	ItemDatabase.initialize()
	
	_logger("Service architecture initialized")

## 向后兼容的玩家数据接口
func get_player_data() -> Dictionary:
	if stat_service:
		return stat_service.get_all_stats()
	return {}

func set_player_data(data: Dictionary):
	if stat_service:
		stat_service.set_stats_batch(data)
		_logger("Player data loaded to StatService")

func update_player_data(key: String, value: Variant):
	if stat_service:
		stat_service.set_stat(key, value)
	# HUD刷新由stats_changed信号处理

## 统计数据变化处理
func _on_stats_changed(stat_name: String, old_value, new_value):
	refresh_player_hud()  # 数据更新后刷新HUD
	_logger("Stat changed: %s = %s (was %s)" % [stat_name, new_value, old_value])

# ================================
# 玩家数据管理 (兼容接口)
# ================================

## 重置玩家数据到默认值
func reset_player_data():
	if stat_service:
		stat_service.reset_to_defaults()
	_logger("Player data reset to defaults")

## 获取应该使用的保存槽位（考虑新游戏的预期槽位）
func get_save_slot_for_current_game() -> int:
	if SaveManager:
		var current_slot = SaveManager.get_current_save_slot()
		if current_slot > 0:
			# 如果已有当前槽位，使用它
			return current_slot
		elif intended_save_slot > 0:
			# 如果是新游戏，使用预期槽位
			return intended_save_slot
	
	# 默认使用槽位1
	return 1

# ================================
# 游戏流程控制 (更新版)
# ================================

func new_game(slot: int = 1):
	# 重置玩家数据到默认值
	reset_player_data()

	# 记住预期的保存槽位
	intended_save_slot = slot
	
	# 清除当前存档槽位，因为这是新游戏
	if SaveManager:
		SaveManager.set_current_save_slot(-1)

	# 状态切换到游戏中 - AppStateManager会处理UI创建
	if AppStateManager:
		AppStateManager.start_game()

	# 场景切换
	if SceneManager:
		SceneManager.go_to_main_school()

	# 启动时间效果演示（新游戏时启动）
	start_time_effect_demo()
	
	# 添加测试物品到背包
	add_test_inventory_items()

	_logger("New game started with service architecture, intended slot: %d" % slot)

# ================================
# 管理器协调设置
# ================================

## 设置管理器间的协调
func _setup_manager_coordination():
	# 确保SettingsManager应用初始设置
	if SettingsManager:
		SettingsManager.apply_initial_settings()
	
	_logger("Manager coordination setup complete")

## 连接状态管理器信号以正确管理UI生命周期
func _connect_state_manager_signals():
	if AppStateManager:
		AppStateManager.state_changed.connect(_on_app_state_changed)
		_logger("Connected to AppStateManager signals")

# ================================
# 状态管理接口 (供UI层调用)
# ================================

## 开始新游戏
func start_new_game(slot: int = 1):
	new_game(slot)

## 打开存档选择界面 (按需创建，用完即销毁)
func open_save_select():
	# 确保在正确的状态下打开存档选择
	if AppStateManager.current_state != AppStateManager.AppState.MAIN_MENU:
		_logger("Save select can only be opened from MAIN_MENU state")
		return
	
	# 按需创建存档选择界面
	var save_select_scene = preload("res://Scene/UI/save_select_ui.tscn")
	if save_select_scene:
		var save_select_ui = save_select_scene.instantiate()
		get_tree().root.add_child(save_select_ui)
		save_select_ui.show()
		# 槽位会在_ready()中自动创建和刷新
		_logger("Save select UI created and opened")
	else:
		_logger("Failed to load save select UI scene")

## 进入设置菜单
func open_settings():
	if AppStateManager:
		AppStateManager.open_settings()

## 打开背包页面
func open_inventory():
	if AppStateManager:
		AppStateManager.open_inventory()

## 从设置返回
func back_from_settings():
	if AppStateManager:
		AppStateManager.back_from_settings()

## 继续游戏（从指定槽位加载）
func continue_game(slot: int = -1) -> bool:
	if not SaveManager:
		_logger("SaveManager not available")
		return false
	
	# 清除新游戏的预期槽位，因为现在是加载存档
	intended_save_slot = -1
	
	# 如果没有指定槽位，使用快速加载（最新存档）
	var load_success: bool = false
	if slot == -1:
		load_success = SaveManager.quick_load()
	else:
		# 检查存档是否存在
		if not SaveManager.has_save(slot):
			_logger("Save does not exist in slot %d" % slot)
			return false
		load_success = SaveManager.load_game(slot)
	
	if load_success:
		# 切换到游戏状态 - AppStateManager会处理PlayerHUD创建
		if AppStateManager:
			AppStateManager.start_game()
		_logger("Game continued successfully from slot %d" % (slot if slot != -1 else SaveManager.get_current_save_slot()))
		return true
	else:
		_logger("Failed to continue game")
		return false

## 快速继续（加载最新存档）
func quick_continue() -> bool:
	return continue_game(-1)

## 检查是否有存档可以继续
func can_continue_game() -> bool:
	if not SaveManager:
		return false
	# 检查是否有任何存档（排除自动保存槽位0）
	for i in range(1, SaveManager.MAX_SAVE_SLOTS):
		if SaveManager.has_save(i):
			return true
	return false

## 获取存档列表信息
func get_save_list() -> Array:
	if SaveManager:
		return SaveManager.get_all_save_info()
	return []

## 删除存档
func delete_save(slot: int) -> bool:
	if SaveManager:
		return SaveManager.delete_save(slot)
	return false

## 进入主菜单
func go_to_main_menu():
	# AppStateManager会处理UI生命周期，无需手动销毁HUD
	if AppStateManager:
		AppStateManager.go_to_main_menu()

## 退出应用
func quit_application():
	# 停止自动保存
	if SaveManager:
		SaveManager.stop_autosave()
	# AppStateManager会处理所有清理工作
	if AppStateManager:
		AppStateManager.quit_application()
	# AppStateManager.QUIT_APP状态会调用get_tree().quit()

## 统一日志输出函数
## 响应应用状态变化 - 正确管理UI生命周期
func _on_app_state_changed(old_state: AppStateManager.AppState, new_state: AppStateManager.AppState):
	_logger("App state changed: %s -> %s" % [AppStateManager.AppState.keys()[old_state], AppStateManager.AppState.keys()[new_state]])
	
	# 进入游戏状态时创建PlayerHUD
	if new_state == AppStateManager.AppState.IN_GAME:
		# 等待场景切换完成后创建HUD
		await get_tree().process_frame
		_setup_player_hud()
		set_player_hud_visible(true)
		refresh_player_hud()
	
	# 离开游戏状态时销毁PlayerHUD
	elif old_state == AppStateManager.AppState.IN_GAME and new_state != AppStateManager.AppState.PAUSED:
		_destroy_player_hud()

func _logger(msg: String) -> void:
	print("[GameManager] %s" % msg)

# ================================
# UI 管理
# ================================

## 设置玩家HUD
func _setup_player_hud():
	# 如果已经存在HUD，先销毁它
	if player_hud:
		player_hud.queue_free()
		player_hud = null
	
	var hud_scene = preload("res://Scene/UI/player_hud.tscn")
	if hud_scene:
		player_hud = hud_scene.instantiate()
		get_tree().root.call_deferred("add_child", player_hud)
		_logger("Player HUD created and will be added to scene tree")
	else:
		_logger("Failed to load player HUD scene")

## 销毁玩家HUD
func _destroy_player_hud():
	if player_hud:
		player_hud.queue_free()
		player_hud = null
		_logger("Player HUD destroyed")

## 显示/隐藏玩家HUD
func set_player_hud_visible(visible: bool):
	if player_hud and player_hud.has_method("set_hud_visible"):
		player_hud.set_hud_visible(visible)

## 刷新玩家HUD显示
func refresh_player_hud():
	if player_hud and player_hud.has_method("refresh_hud"):
		player_hud.refresh_hud()

# ================================
# 时间效果控制流程
# ================================

## 启用时间效果（传递给TimeManager处理）
func enable_time_effect(start_hour: float = 12.0):
	if not TimeManager:
		_logger("TimeManager not available")
		return
	
	# 确保在场景准备好后再调用
	if not get_tree() or not get_tree().current_scene:
		await get_tree().process_frame
	
	TimeManager.enable_time_effect(start_hour)
	_logger("Time effect enabled with start hour: %.1f" % start_hour)

## 关闭时间效果
func disable_time_effect():
	if TimeManager:
		TimeManager.disable_time_effect()
		_logger("Time effect disabled")

## 平滑过渡时间变化
func smooth_time_transition(start_hour: float, end_hour: float, duration: float):
	if TimeManager:
		TimeManager.smooth_transition(start_hour, end_hour, duration)
		_logger("Time transition: %.1f -> %.1f over %.1fs" % [start_hour, end_hour, duration])

# ================================
# 测试和演示管理 (可通过注释控制)
# ================================

## 时间效果演示实例
var time_effect_demo: TimeEffectDemo = null

## 启动时间效果演示（可注释此调用来禁用）
func start_time_effect_demo():
	# TODO: 如果不需要时间演示，注释掉下面这行即可
	_run_time_effect_demo()

## 实际运行时间效果演示的函数
func _run_time_effect_demo():
	if time_effect_demo and time_effect_demo.is_demo_completed():
		_logger("时间效果演示已完成，跳过")
		return
	
	# 如果还没有创建演示实例，创建一个
	if not time_effect_demo:
		var demo_script = preload("res://Script/TestScript/time_effect_demo.gd")
		time_effect_demo = demo_script.new()
		time_effect_demo.name = "TimeEffectDemo"
		add_child(time_effect_demo)
		_logger("时间效果演示实例已创建")
	
	# 启动演示
	time_effect_demo.start_demo()

## 停止时间效果演示
func stop_time_effect_demo():
	if time_effect_demo:
		time_effect_demo.stop_demo()

## 强制重启时间效果演示（用于调试）
func restart_time_effect_demo():
	if time_effect_demo:
		time_effect_demo.restart_demo()
	else:
		_run_time_effect_demo()

## 检查时间效果演示是否完成
func is_time_demo_completed() -> bool:
	if time_effect_demo:
		return time_effect_demo.is_demo_completed()
	return false

# ================================
# 背包系统测试功能 (调试用) - 更新版
# ================================

## 添加测试物品到背包
func add_test_inventory_items():
	if InventoryManager:
		InventoryManager.clear_inventory()
		# 使用新物品数据库中的物品
		InventoryManager.add_item("potion_health", 5)
		InventoryManager.add_item("bread", 10)
		InventoryManager.add_item("sword_iron", 1)
		InventoryManager.add_item("armor_leather", 1)
		InventoryManager.add_item("iron_ore", 20)
		InventoryManager.add_item("coin_gold", 100)
		_logger("Added test items from ItemDatabase to inventory")
	else:
		_logger("InventoryManager not available")

## 测试新的标签系统
func test_tag_system():
	ItemDatabase.initialize()
	
	# 测试几个物品的标签
	var test_items = ["potion_health", "sword_iron", "armor_leather"]
	for item_id in test_items:
		var item = ItemDatabase.get_item(item_id)
		if item:
			TagUtil.debug_print_item_tags(item)
		else:
			_logger("Test item not found: %s" % item_id)

## 测试服务集成
func debug_service_status():
	_logger("=== Service Architecture Status ===")
	_logger("StatService: %s" % ("OK" if stat_service else "Missing"))
	_logger("ItemEffectService: %s" % ("OK" if item_effect_service else "Missing"))
	_logger("ItemDatabase initialized: %s" % ItemDatabase._initialized)
	_logger("Registered tag handlers: %s" % TagUtil.get_registered_handlers())
	
	if stat_service:
		stat_service.debug_print_stats()
	
	if item_effect_service:
		item_effect_service.debug_print_status()
