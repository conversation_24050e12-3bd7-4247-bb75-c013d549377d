extends Node

## 游戏内状态机 - 管理游戏内循环状态（仅在AppStateManager为IN_GAME时运行）
## 对应状态机设计图的第二层嵌套状态机

## 游戏内状态枚举
enum GameplayState {
	IDLE,           ## 空闲状态（未开始游戏循环）
	REAL_WORLD,     ## 现实世界状态
	DREAM_WORLD,    ## 梦境世界状态
	BATTLE,         ## 普通战斗
	BOSS_BATTLE,    ## Boss战斗
	CHAPTER_END     ## 章节结束
}

## 现实世界子状态枚举
enum RealWorldState {
	DAY,        ## 白天
	NIGHT,      ## 夜晚
	BOSS_DAY    ## Boss日
}

## 当前游戏状态
var current_state: GameplayState = GameplayState.IDLE
var real_world_state: RealWorldState = RealWorldState.DAY

## 状态改变信号
signal gameplay_state_changed(old_state: GameplayState, new_state: GameplayState)
signal real_world_state_changed(old_state: RealWorldState, new_state: RealWorldState)

## 游戏失败信号 - 通知AppStateManager进入GameOver状态
signal player_defeated()

## 游戏数据状态
var is_gameplay_active: bool = false

func _ready():
	_logger("Gameplay State Manager Ready!")
	# 连接失败信号到AppStateManager
	player_defeated.connect(_on_player_defeated)

## 启动游戏循环（由AppStateManager调用）
func start_gameplay() -> void:
	if is_gameplay_active:
		return
	
	is_gameplay_active = true
	_logger("Starting gameplay loop...")
	change_state(GameplayState.REAL_WORLD)

## 停止游戏循环（由AppStateManager调用）
func stop_gameplay() -> void:
	if not is_gameplay_active:
		return
	
	is_gameplay_active = false
	_logger("Stopping gameplay loop...")
	change_state(GameplayState.IDLE)

## 状态转换函数
func change_state(new_state: GameplayState) -> void:
	if not is_gameplay_active and new_state != GameplayState.IDLE:
		push_warning("Cannot change gameplay state when gameplay is not active")
		return
	
	var old_state = current_state
	
	# 状态转换验证
	if not _is_valid_transition(old_state, new_state):
		push_warning("Invalid gameplay state transition from %s to %s" % [GameplayState.keys()[old_state], GameplayState.keys()[new_state]])
		return
	
	_logger("Gameplay State: %s -> %s" % [GameplayState.keys()[old_state], GameplayState.keys()[new_state]])
	
	# 退出当前状态
	_exit_gameplay_state(old_state)
	
	# 更新状态
	current_state = new_state
	
	# 进入新状态
	_enter_gameplay_state(new_state)
	
	# 发送状态改变信号
	gameplay_state_changed.emit(old_state, new_state)

## 现实世界子状态转换
func change_real_world_state(new_state: RealWorldState) -> void:
	if current_state != GameplayState.REAL_WORLD:
		push_warning("Cannot change real world state when not in REAL_WORLD")
		return
	
	var old_state = real_world_state
	
	# 验证现实世界状态转换
	if not _is_valid_real_world_transition(old_state, new_state):
		push_warning("Invalid real world state transition from %s to %s" % [RealWorldState.keys()[old_state], RealWorldState.keys()[new_state]])
		return
	
	_logger("Real World State: %s -> %s" % [RealWorldState.keys()[old_state], RealWorldState.keys()[new_state]])
	
	# 退出当前现实世界状态
	_exit_real_world_state(old_state)
	
	# 更新状态
	real_world_state = new_state
	
	# 进入新现实世界状态
	_enter_real_world_state(new_state)
	
	# 发送状态改变信号
	real_world_state_changed.emit(old_state, new_state)

## 验证游戏内状态转换是否合法
func _is_valid_transition(from_state: GameplayState, to_state: GameplayState) -> bool:
	match from_state:
		GameplayState.IDLE:
			return to_state == GameplayState.REAL_WORLD
		GameplayState.REAL_WORLD:
			return to_state in [GameplayState.DREAM_WORLD, GameplayState.IDLE]
		GameplayState.DREAM_WORLD:
			return to_state in [GameplayState.BATTLE, GameplayState.CHAPTER_END, GameplayState.REAL_WORLD, GameplayState.IDLE]
		GameplayState.BATTLE:
			return to_state in [GameplayState.DREAM_WORLD, GameplayState.BOSS_BATTLE, GameplayState.IDLE]
		GameplayState.BOSS_BATTLE:
			return to_state in [GameplayState.DREAM_WORLD, GameplayState.IDLE]
		GameplayState.CHAPTER_END:
			return to_state in [GameplayState.REAL_WORLD, GameplayState.IDLE]
	return false

## 验证现实世界状态转换是否合法
func _is_valid_real_world_transition(from_state: RealWorldState, to_state: RealWorldState) -> bool:
	match from_state:
		RealWorldState.DAY:
			return to_state == RealWorldState.NIGHT
		RealWorldState.NIGHT:
			return to_state == RealWorldState.BOSS_DAY
		RealWorldState.BOSS_DAY:
			return to_state == RealWorldState.DAY
	return false

## 退出游戏状态时的处理
func _exit_gameplay_state(state: GameplayState) -> void:
	match state:
		GameplayState.REAL_WORLD:
			# 清理现实世界相关资源
			pass
		GameplayState.DREAM_WORLD:
			# 清理梦境世界相关资源
			pass
		GameplayState.BATTLE:
			# 清理战斗相关资源
			pass
		GameplayState.BOSS_BATTLE:
			# 清理Boss战斗相关资源
			pass
		GameplayState.CHAPTER_END:
			# 清理章节结束相关资源
			pass

## 进入游戏状态时的处理
func _enter_gameplay_state(state: GameplayState) -> void:
	match state:
		GameplayState.IDLE:
			# 重置所有游戏状态
			real_world_state = RealWorldState.DAY
		GameplayState.REAL_WORLD:
			# 初始化现实世界
			# 可能需要加载特定的现实世界场景
			pass
		GameplayState.DREAM_WORLD:
			# 初始化梦境世界
			# 可能需要加载梦境场景
			pass
		GameplayState.BATTLE:
			# 初始化战斗系统
			pass
		GameplayState.BOSS_BATTLE:
			# 初始化Boss战斗
			pass
		GameplayState.CHAPTER_END:
			# 显示章节结束界面
			pass

## 退出现实世界状态时的处理
func _exit_real_world_state(state: RealWorldState) -> void:
	match state:
		RealWorldState.DAY:
			# 清理白天相关资源
			pass
		RealWorldState.NIGHT:
			# 清理夜晚相关资源
			pass
		RealWorldState.BOSS_DAY:
			# 清理Boss日相关资源
			pass

## 进入现实世界状态时的处理
func _enter_real_world_state(state: RealWorldState) -> void:
	match state:
		RealWorldState.DAY:
			# 设置白天环境
			_logger("进入白天状态")
		RealWorldState.NIGHT:
			# 设置夜晚环境
			_logger("进入夜晚状态")
		RealWorldState.BOSS_DAY:
			# 设置Boss日环境
			_logger("进入Boss日状态")

## 游戏事件触发方法 - 供游戏逻辑调用

## 压力值达到最大，从白天转入夜晚
func pressure_maxed() -> void:
	if current_state == GameplayState.REAL_WORLD and real_world_state == RealWorldState.DAY:
		change_real_world_state(RealWorldState.NIGHT)

## 进入Boss门，从夜晚转入Boss日
func enter_boss_gate() -> void:
	if current_state == GameplayState.REAL_WORLD and real_world_state == RealWorldState.NIGHT:
		change_real_world_state(RealWorldState.BOSS_DAY)

## Boss被击败，从Boss日回到白天
func boss_defeated() -> void:
	if current_state == GameplayState.REAL_WORLD and real_world_state == RealWorldState.BOSS_DAY:
		change_real_world_state(RealWorldState.DAY)

## 入睡，从现实世界进入梦境世界
func fall_asleep() -> void:
	if current_state == GameplayState.REAL_WORLD:
		change_state(GameplayState.DREAM_WORLD)

## 遇到敌人，从梦境进入战斗
func encounter_enemy() -> void:
	if current_state == GameplayState.DREAM_WORLD:
		change_state(GameplayState.BATTLE)

## 战斗胜利，回到梦境世界
func win_battle() -> void:
	if current_state == GameplayState.BATTLE:
		change_state(GameplayState.DREAM_WORLD)

## 触发Boss战
func trigger_boss_battle() -> void:
	if current_state == GameplayState.BATTLE:
		change_state(GameplayState.BOSS_BATTLE)

## Boss战胜利，回到梦境世界
func boss_battle_victory() -> void:
	if current_state == GameplayState.BOSS_BATTLE:
		change_state(GameplayState.DREAM_WORLD)

## 完成目标，进入章节结束
func objectives_complete() -> void:
	if current_state == GameplayState.DREAM_WORLD:
		change_state(GameplayState.CHAPTER_END)

## 醒来，从章节结束回到现实世界
func wake_up() -> void:
	if current_state == GameplayState.CHAPTER_END:
		change_state(GameplayState.REAL_WORLD)

## 玩家失败 - 触发Game Over
func trigger_player_defeated() -> void:
	player_defeated.emit()

## 处理玩家失败信号
func _on_player_defeated() -> void:
	if AppStateManager:
		AppStateManager.game_over()

## 获取当前状态信息
func get_current_state() -> GameplayState:
	return current_state

func get_current_real_world_state() -> RealWorldState:
	return real_world_state

func get_current_state_name() -> String:
	return GameplayState.keys()[current_state]

func get_current_real_world_state_name() -> String:
	return RealWorldState.keys()[real_world_state]

## 判断是否处于特定状态
func is_in_real_world() -> bool:
	return current_state == GameplayState.REAL_WORLD

func is_in_dream_world() -> bool:
	return current_state == GameplayState.DREAM_WORLD

func is_in_battle() -> bool:
	return current_state in [GameplayState.BATTLE, GameplayState.BOSS_BATTLE]

func is_day_time() -> bool:
	return current_state == GameplayState.REAL_WORLD and real_world_state == RealWorldState.DAY

func is_night_time() -> bool:
	return current_state == GameplayState.REAL_WORLD and real_world_state == RealWorldState.NIGHT

func is_boss_day() -> bool:
	return current_state == GameplayState.REAL_WORLD and real_world_state == RealWorldState.BOSS_DAY

## 统一日志输出函数
func _logger(msg: String) -> void:
	print("[GameplayStateManager] %s" % msg)
