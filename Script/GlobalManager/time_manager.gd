extends Node

# 极简时间管理器 - 全局单例
# 仅提供3个核心功能：开启、关闭、平滑过渡
# 重构后：使用 SceneManager 来获取当前场景，避免重复逻辑

var time_overlay_materials: Array[ShaderMaterial] = []
var current_hour: float = 12.0
var transition_tween: Tween
var time_overlay: CanvasLayer = null

# 当前时间效果绑定的场景
var _bound_scene: Node = null

func _ready():
	# 监听场景变化，自动重新绑定时间效果
	if SceneManager:
		SceneManager.scene_changed.connect(_on_scene_changed)

# 场景变化时的处理
func _on_scene_changed(old_scene_path: String, new_scene_path: String):
	print("[TimeManager] Scene changed: %s -> %s" % [old_scene_path, new_scene_path])

	# 如果时间效果正在运行，自动重新绑定到新场景
	if time_overlay != null:
		var _was_active = true
		var saved_hour = current_hour

		# 先关闭当前效果
		disable_time_effect()

		# 等待一帧确保清理完成
		await get_tree().process_frame

		# 重新启用时间效果
		enable_time_effect(saved_hour)
		print("[TimeManager] Time effect automatically rebound to new scene")

# 开启时间效果（动态创建覆盖层并设置起始时间）
func enable_time_effect(start_hour: float = 12.0):
	var scene = _get_current_scene()
	if not scene:
		print("[TimeManager] Cannot find current scene")
		return

	_create_time_overlay(scene)
	_bound_scene = scene
	current_hour = clamp(start_hour, 0.0, 23.999)
	_update_all_materials()

# 获取当前场景的简洁方法 - 直接使用 SceneManager
func _get_current_scene() -> Node:
	# 使用 SceneManager 的 current_scene，它负责维护当前场景状态
	if SceneManager and SceneManager.current_scene:
		print("[TimeManager] Using SceneManager.current_scene: %s" % SceneManager.current_scene.name)
		return SceneManager.current_scene

	print("[TimeManager] No current scene available")
	return null

# 关闭时间效果（销毁覆盖层）
func disable_time_effect():
	_destroy_time_overlay()
	time_overlay_materials.clear()
	_bound_scene = null

# 检查时间效果是否激活
func is_time_effect_active() -> bool:
	return time_overlay != null and is_instance_valid(time_overlay)

# 获取当前绑定的场景
func get_bound_scene() -> Node:
	return _bound_scene

# 检查时间效果是否绑定到指定场景
func is_bound_to_scene(scene: Node) -> bool:
	return _bound_scene == scene and is_time_effect_active()

# 平滑过渡时间变化
func smooth_transition(start_hour: float, end_hour: float, duration: float):
	if not is_time_effect_active():
		print("[TimeManager] Time overlay not active, call enable_time_effect first")
		return

	if transition_tween:
		transition_tween.kill()

	current_hour = clamp(start_hour, 0.0, 23.999)
	var target_hour = clamp(end_hour, 0.0, 23.999)

	transition_tween = create_tween()
	transition_tween.tween_method(_set_hour, current_hour, target_hour, duration)

# 创建时间覆盖层
func _create_time_overlay(target_scene: Node):
	if not is_instance_valid(target_scene):
		print("[TimeManager] Target scene is invalid, cannot create time overlay")
		return

	# 如果已经存在，先销毁
	if time_overlay:
		_destroy_time_overlay()

	# 创建CanvasLayer
	time_overlay = CanvasLayer.new()
	time_overlay.name = "TimeOverlay"
	time_overlay.layer = 100

	# 创建ColorRect
	var time_color_rect = ColorRect.new()
	time_color_rect.name = "TimeColorRect"
	
	# 精确匹配手动配置的属性顺序和数值
	time_color_rect.anchors_preset = 15
	time_color_rect.anchor_right = 1.0
	time_color_rect.anchor_bottom = 1.0
	time_color_rect.mouse_filter = 2

	# 创建并设置shader材质
	var shader_material = ShaderMaterial.new()
	var time_shader = load("res://Asset/time_overlay.gdshader")
	if time_shader:
		shader_material.shader = time_shader
		shader_material.set_shader_parameter("time_hour", current_hour)
		shader_material.set_shader_parameter("transition_speed", 1.0)
		time_color_rect.material = shader_material

		# 注册材质
		register_time_overlay(shader_material)
		print("[TimeManager] Time overlay shader loaded successfully, time_hour: %.1f" % current_hour)
	else:
		print("[TimeManager] Warning: time_overlay.gdshader not found")

	# 组装节点树
	time_overlay.add_child(time_color_rect)
	target_scene.add_child(time_overlay)
	print("[TimeManager] Time overlay created and attached to scene: %s" % target_scene.name)

# 销毁时间覆盖层
func _destroy_time_overlay():
	if time_overlay:
		time_overlay.queue_free()
		time_overlay = null

# 注册材质（内部使用）
func register_time_overlay(material: ShaderMaterial):
	if material and material not in time_overlay_materials:
		time_overlay_materials.append(material)
		_update_material(material)

func _set_hour(hour: float):
	current_hour = hour
	_update_all_materials()

func _update_all_materials():
	for material in time_overlay_materials:
		if is_instance_valid(material):
			_update_material(material)
		else:
			time_overlay_materials.erase(material)

func _update_material(material: ShaderMaterial):
	if material and material.shader:
		material.set_shader_parameter("time_hour", current_hour)
