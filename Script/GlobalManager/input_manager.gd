extends Node

## 全局输入处理器 - 专门负责处理全局快捷键和输入事件
## 从 GameManager 中提取的输入处理逻辑，实现单一职责原则

## 信号定义
signal pause_menu_toggle_requested()
#signal settings_requested()
signal quit_requested()

func _ready():
	_logger("Input Manager Ready!")

# ================================
# 全局输入处理
# ================================

## 处理全局快捷键
func _unhandled_input(event):
	# 只在特定状态下处理快捷键
	if not AppStateManager:
		return

	# 暂停菜单切换 (ESC键)
	var should_toggle_pause = false

	# 检查ESC键或menu动作
	if event is InputEventKey and event.pressed:
		if event.keycode == KEY_ESCAPE:
			should_toggle_pause = true

	# 检查menu动作（如果存在）
	if InputMap.has_action("menu") and event.is_action_pressed("menu"):
		should_toggle_pause = true

	if should_toggle_pause:
		_handle_pause_toggle(event)

## 处理暂停切换
func _handle_pause_toggle(event: InputEvent):
	var current_state = AppStateManager.get_current_state()
	
	match current_state:
		AppStateManager.AppState.IN_GAME:
			_logger("Pause toggle requested from IN_GAME state")
			pause_menu_toggle_requested.emit()
			_consume_input(event)
		
		AppStateManager.AppState.PAUSED:
			_logger("Pause toggle requested from PAUSED state")
			pause_menu_toggle_requested.emit()
			_consume_input(event)
		
		AppStateManager.AppState.INVENTORY:
			# 在背包状态下不处理ESC键，让背包页面自己处理
			_logger("Ignoring pause toggle in INVENTORY state")
		
		AppStateManager.AppState.SETTINGS:
			_logger("Back from settings requested")
			# 从设置界面返回
			if AppStateManager:
				AppStateManager.back_from_settings()
			_consume_input(event)
		
		_:
			# 其他状态不处理ESC键
			pass

## 消费输入事件，防止传递给其他节点
func _consume_input(_event: InputEvent):
	var viewport = get_viewport()
	if viewport:
		viewport.set_input_as_handled()

# ================================
# 特殊输入处理
# ================================

## 处理调试快捷键 (开发版本)
func _handle_debug_input(event: InputEvent):
	if not event is InputEventKey or not event.pressed:
		return
	
	# 组合键检测
	var ctrl_pressed = Input.is_key_pressed(KEY_CTRL)
	var shift_pressed = Input.is_key_pressed(KEY_SHIFT)
	var _alt_pressed = Input.is_key_pressed(KEY_ALT)
	
	# Ctrl+Shift+D: 调试信息
	if ctrl_pressed and shift_pressed and event.keycode == KEY_D:
		_show_debug_info()
		_consume_input(event)
	
	# Ctrl+Shift+R: 重新加载当前场景
	elif ctrl_pressed and shift_pressed and event.keycode == KEY_R:
		_reload_current_scene()
		_consume_input(event)
	
	# Ctrl+Shift+Q: 快速退出
	elif ctrl_pressed and shift_pressed and event.keycode == KEY_Q:
		_quick_quit()
		_consume_input(event)

## 显示调试信息
func _show_debug_info():
	_logger("=== Debug Info ===")
	if AppStateManager:
		_logger("App State: %s" % AppStateManager.get_current_state_name())
	if GameplayStateManager:
		_logger("Gameplay State: %s" % GameplayStateManager.get_current_state_name())
	if SaveManager:
		_logger("Current Save Slot: %d" % SaveManager.get_current_save_slot())

## 重新加载当前场景
func _reload_current_scene():
	_logger("Reloading current scene...")
	if SceneManager:
		SceneManager.reload_current_scene()

## 快速退出
func _quick_quit():
	_logger("Quick quit requested")
	quit_requested.emit()

# ================================
# 输入状态查询
# ================================

## 检查是否按下了特定的组合键
func is_combo_pressed(keys: Array[int]) -> bool:
	for key in keys:
		if not Input.is_key_pressed(key):
			return false
	return true

## 检查是否有修饰键被按下
func get_modifier_state() -> Dictionary:
	return {
		"ctrl": Input.is_key_pressed(KEY_CTRL),
		"shift": Input.is_key_pressed(KEY_SHIFT),
		"alt": Input.is_key_pressed(KEY_ALT),
		"meta": Input.is_key_pressed(KEY_META)
	}

## 检查当前是否应该处理输入
func should_handle_input() -> bool:
	if not AppStateManager:
		return false
	
	var current_state = AppStateManager.get_current_state()
	
	# 在这些状态下处理全局输入
	match current_state:
		AppStateManager.AppState.IN_GAME, \
		AppStateManager.AppState.PAUSED, \
		AppStateManager.AppState.SETTINGS:
			return true
		_:
			return false

# ================================
# 输入映射工具
# ================================

## 获取动作的当前按键字符串
func get_action_key_string(action: String) -> String:
	if not InputMap.has_action(action):
		return "Not found"
	
	var events = InputMap.action_get_events(action)
	if events.size() > 0 and events[0] is InputEventKey:
		var key_event = events[0] as InputEventKey
		return OS.get_keycode_string(key_event.keycode)
	
	return "No key assigned"

## 检查动作是否被按下（带状态检查）
func is_action_just_pressed_safe(action: String) -> bool:
	if not should_handle_input():
		return false
	
	if not InputMap.has_action(action):
		return false
	
	return Input.is_action_just_pressed(action)

## 检查动作是否被释放（带状态检查）
func is_action_just_released_safe(action: String) -> bool:
	if not should_handle_input():
		return false
	
	if not InputMap.has_action(action):
		return false
	
	return Input.is_action_just_released(action)

# ================================
# 输入事件过滤
# ================================

## 过滤不需要的输入事件
func should_ignore_input(event: InputEvent) -> bool:
	# 忽略鼠标移动事件（除非特别需要）
	if event is InputEventMouseMotion:
		return true
	
	# 忽略重复的按键事件
	if event is InputEventKey and event.echo:
		return true
	
	# 在特定状态下忽略所有输入
	if AppStateManager:
		var current_state = AppStateManager.get_current_state()
		if current_state == AppStateManager.AppState.QUIT_APP:
			return true
	
	return false

# ================================
# 输入历史记录（调试用）
# ================================

var input_history: Array[Dictionary] = []
const MAX_HISTORY_SIZE = 50

## 记录输入事件（调试用）
func record_input_event(event: InputEvent):
	var record = {
		"timestamp": Time.get_ticks_msec(),
		"type": event.get_class(),
		"details": _get_event_details(event)
	}
	
	input_history.append(record)
	
	# 限制历史记录大小
	if input_history.size() > MAX_HISTORY_SIZE:
		input_history.pop_front()

## 获取事件详细信息
func _get_event_details(event: InputEvent) -> String:
	if event is InputEventKey:
		var key_event = event as InputEventKey
		return "Key: %s, Pressed: %s" % [OS.get_keycode_string(key_event.keycode), key_event.pressed]
	elif event is InputEventMouseButton:
		var mouse_event = event as InputEventMouseButton
		return "Mouse: Button %d, Pressed: %s" % [mouse_event.button_index, mouse_event.pressed]
	else:
		return event.as_text()

## 打印输入历史（调试用）
func print_input_history():
	_logger("=== Input History ===")
	for i in range(max(0, input_history.size() - 10), input_history.size()):
		var record = input_history[i]
		_logger("[%d] %s: %s" % [record.timestamp, record.type, record.details])

# ================================
# 公共接口
# ================================

## 启用调试模式
var debug_mode: bool = false

func set_debug_mode(enabled: bool):
	debug_mode = enabled
	_logger("Debug mode %s" % ("enabled" if enabled else "disabled"))

## 获取当前输入状态摘要
func get_input_summary() -> Dictionary:
	return {
		"should_handle": should_handle_input(),
		"modifiers": get_modifier_state(),
		"app_state": AppStateManager.get_current_state_name() if AppStateManager else "Unknown",
		"debug_mode": debug_mode
	}

## 统一日志输出函数
func _logger(msg: String) -> void:
	print("[InputManager] %s" % msg)
