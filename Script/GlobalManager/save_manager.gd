extends Node

## 存档管理器 - 支持多存档槽和自动保存功能
## 作为autoload运行，提供全局存档功能

## 存档相关常量
const SAVE_FILE_EXTENSION = ".save"
const SAVE_FOLDER = "user://saves/"
const AUTOSAVE_SLOT = 0  # 自动保存使用槽位0
const MAX_SAVE_SLOTS = 10  # 最多支持10个存档槽

## 设置文件相关常量
const SETTINGS_FILE_NAME = "settings.cfg"
const SETTINGS_FILE_PATH = "user://" + SETTINGS_FILE_NAME
const DEFAULT_SETTINGS_SECTION = "game_settings"

## 自动保存设置
var autosave_enabled: bool = false
var autosave_interval: float = 120.0  # 自动保存间隔（秒）
var autosave_timer: Timer

## 当前活跃的存档槽位（-1表示无活跃存档）
var current_save_slot: int = -1

## 临时传送位置存储（用于场景间传送时保持坐标）
var teleport_position: Vector2 = Vector2.ZERO

## 存档数据结构
var save_data: Dictionary = {}

## 设置数据
var settings_config: ConfigFile
var settings_data: Dictionary = {}

## 信号定义
signal save_completed(slot: int, success: bool)
signal load_completed(slot: int, success: bool)
signal autosave_completed(success: bool)
signal settings_changed(key: String, value: Variant)

func _ready():
	_logger("Save Manager Ready!")
	_ensure_save_directory()
	_setup_autosave_timer()
	_load_settings()

## 确保存档目录存在
func _ensure_save_directory():
	if not DirAccess.dir_exists_absolute(SAVE_FOLDER):
		DirAccess.open("user://").make_dir_recursive("saves")

## 设置自动保存计时器
func _setup_autosave_timer():
	autosave_timer = Timer.new()
	autosave_timer.wait_time = autosave_interval
	autosave_timer.timeout.connect(_on_autosave_timer_timeout)
	autosave_timer.autostart = false
	add_child(autosave_timer)

## 自动保存计时器回调
func _on_autosave_timer_timeout():
	if autosave_enabled and current_save_slot != -1 and current_save_slot != AUTOSAVE_SLOT:
		autosave()

## 开始自动保存
func start_autosave():
	if autosave_enabled:
		autosave_timer.start()
		_logger("Autosave started, interval: %s seconds" % autosave_interval)

## 停止自动保存
func stop_autosave():
	autosave_timer.stop()
	_logger("Autosave stopped")

## 设置自动保存间隔
func set_autosave_interval(interval: float):
	autosave_interval = interval
	autosave_timer.wait_time = interval
	_logger("Autosave interval set to: %s seconds" % interval)

## 启用/禁用自动保存
func set_autosave_enabled(enabled: bool):
	autosave_enabled = enabled
	if enabled and current_save_slot != -1 and current_save_slot != AUTOSAVE_SLOT:
		start_autosave()
	else:
		stop_autosave()
	_logger("Autosave %s" % ("enabled" if enabled else "disabled"))

## 获取存档文件路径
func _get_save_file_path(slot: int) -> String:
	return SAVE_FOLDER + "slot_%d%s" % [slot, SAVE_FILE_EXTENSION]

## 检查存档槽是否存在
func has_save(slot: int) -> bool:
	if slot < 0 or slot >= MAX_SAVE_SLOTS:
		return false
	return FileAccess.file_exists(_get_save_file_path(slot))

## 获取存档信息
func get_save_info(slot: int) -> Dictionary:
	if not has_save(slot):
		return {}
	
	var file = FileAccess.open(_get_save_file_path(slot), FileAccess.READ)
	if file == null:
		return {}
	
	var json_string = file.get_as_text()
	file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	if parse_result != OK:
		_logger("Failed to parse save info, slot: %d" % slot)
		return {}
	
	var data = json.data
	if not data.has("metadata"):
		return {}
	
	return data.metadata

## 获取所有存档槽的信息
func get_all_save_info() -> Array:
	var save_list = []
	for i in range(MAX_SAVE_SLOTS):
		var info = get_save_info(i)
		if info.is_empty():
			save_list.append(null)
		else:
			save_list.append(info)
	return save_list

## 收集当前游戏状态数据
func _collect_save_data() -> Dictionary:
	var data = {
		"metadata": {
			"slot": current_save_slot,
			"save_time": Time.get_datetime_string_from_system(),
			"game_version": "1.0.0",
			"playtime": 0.0,  # 可以从其他管理器获取
			"level": 1,       # 可以从游戏状态获取
			"location": "unknown"  # 可以从场景管理器获取
		},
		"game_state": {},
		"player_data": {},
		"world_data": {}
	}
	
	# 收集玩家数据
	if GameManager:
		data.player_data = GameManager.get_player_data()
	
	# 收集背包数据
	if InventoryManager:
		data.player_data["inventory"] = InventoryManager.get_inventory_data()
	
	# 收集玩家位置数据
	var player_position_data = _collect_player_position()
	if not player_position_data.is_empty():
		data.player_data["position"] = player_position_data
	
	# 收集应用状态
	if AppStateManager:
		data.game_state["app_state"] = AppStateManager.get_current_state()
		data.game_state["app_state_name"] = AppStateManager.get_current_state_name()
	
	# 收集游戏内状态
	if GameplayStateManager:
		data.game_state["gameplay_state"] = GameplayStateManager.get_current_state()
		data.game_state["gameplay_state_name"] = GameplayStateManager.get_current_state_name()
		data.game_state["real_world_state"] = GameplayStateManager.get_current_real_world_state()
		data.game_state["real_world_state_name"] = GameplayStateManager.get_current_real_world_state_name()
	
	# 收集场景信息
	if SceneManager and SceneManager.current_scene:
		data.metadata["location"] = SceneManager.current_scene.scene_file_path
	else:
		data.metadata["location"] = "unknown"
	
	return data

## 收集玩家位置数据
func _collect_player_position() -> Dictionary:
	var position_data = {}
	
	# 查找当前场景中的玩家对象
	var current_scene = get_tree().current_scene
	if not current_scene:
		# 如果get_tree().current_scene为null，尝试从SceneManager获取
		if SceneManager and SceneManager.current_scene:
			current_scene = SceneManager.current_scene
			_logger("Using SceneManager.current_scene for player position collection")
		else:
			_logger("No current scene found for player position collection")
			return position_data
	
	# 寻找TestRunner类型的玩家对象
	var player = _find_player_in_scene(current_scene)
	if player:
		position_data = {
			"global_position": {
				"x": player.global_position.x,
				"y": player.global_position.y
			},
			"scene_path": current_scene.scene_file_path
		}
		_logger("Player position collected: %s in scene %s" % [player.global_position, current_scene.scene_file_path])
	else:
		# 如果是新游戏场景切换中，使用默认位置
		if current_scene.scene_file_path.ends_with("main_school.tscn"):
			# main_school场景的默认玩家位置（从.tscn文件中看到）
			position_data = {
				"global_position": {
					"x": 1984.0,
					"y": 2496.0
				},
				"scene_path": current_scene.scene_file_path
			}
			_logger("Using default player position for main_school: (1984, 2496)")
		else:
			_logger("Player object not found in current scene")
	
	return position_data

## 在场景中查找玩家对象
func _find_player_in_scene(scene_node: Node) -> TestRunner:
	# 首先检查当前节点是否是玩家
	if scene_node is TestRunner:
		return scene_node as TestRunner
	
	# 递归查找子节点
	for child in scene_node.get_children():
		var player = _find_player_in_scene(child)
		if player:
			return player
	
	return null

## 恢复玩家位置
func _apply_player_position(loaded_save_data: Dictionary):
	_logger("_apply_player_position called")
	
	# 检查存档数据中是否有玩家位置信息
	if not loaded_save_data.has("player_data"):
		_logger("Save data has no player_data section")
		return
		
	if not loaded_save_data.player_data.has("position"):
		_logger("Player data has no position section")
		_logger("Player data keys: %s" % loaded_save_data.player_data.keys())
		return
	
	var position_data = loaded_save_data.player_data.position
	_logger("Found position data: %s" % position_data)
	
	# 验证位置数据完整性
	if not position_data.has("global_position") or not position_data.global_position.has("x") or not position_data.global_position.has("y"):
		_logger("Invalid player position data format")
		return
	
	# 查找当前场景中的玩家对象
	var current_scene = get_tree().current_scene
	if not current_scene:
		# 如果get_tree().current_scene为null，尝试从SceneManager获取
		if SceneManager and SceneManager.current_scene:
			current_scene = SceneManager.current_scene
			_logger("Using SceneManager.current_scene for player position restore")
		else:
			_logger("No current scene found for player position restore")
			return
	
	_logger("Current scene: %s" % current_scene.scene_file_path)
	
	var player = _find_player_in_scene(current_scene)
	if not player:
		_logger("Player object not found in current scene for position restore")
		return
	
	_logger("Found player object: %s" % player)
	
	# 恢复玩家位置
	var restored_position = Vector2(
		position_data.global_position.x,
		position_data.global_position.y
	)
	
	_logger("Restoring player position from %s to %s" % [player.global_position, restored_position])
	player.global_position = restored_position
	_logger("Player position restored to: %s" % restored_position)

## 保存游戏到指定槽位
func save_game(slot: int) -> bool:
	if slot < 0 or slot >= MAX_SAVE_SLOTS:
		_logger("Invalid save slot: %d" % slot)
		save_completed.emit(slot, false)
		return false

	var data = _collect_save_data()
	data.metadata.slot = slot

	var file = FileAccess.open(_get_save_file_path(slot), FileAccess.WRITE)
	if file == null:
		_logger("Cannot create save file, slot: %d" % slot)
		save_completed.emit(slot, false)
		return false

	var json_string = JSON.stringify(data, "\t")
	file.store_string(json_string)
	file.close()

	# 只有非自动保存槽位才更新当前槽位
	if slot != AUTOSAVE_SLOT:
		current_save_slot = slot
		_logger("Current save slot updated to: %d" % slot)
	else:
		_logger("Autosave completed, current slot unchanged: %d" % current_save_slot)

	save_data = data

	_logger("Game saved to slot: %d" % slot)
	save_completed.emit(slot, true)
	return true

## 从指定槽位加载游戏
func load_game(slot: int) -> bool:
	if not has_save(slot):
		_logger("Save does not exist, slot: %d" % slot)
		load_completed.emit(slot, false)
		return false

	var file = FileAccess.open(_get_save_file_path(slot), FileAccess.READ)
	if file == null:
		_logger("Cannot open save file, slot: %d" % slot)
		load_completed.emit(slot, false)
		return false

	var json_string = file.get_as_text()
	file.close()

	var json = JSON.new()
	var parse_result = json.parse(json_string)
	if parse_result != OK:
		_logger("Failed to parse save file, slot: %d" % slot)
		load_completed.emit(slot, false)
		return false

	var data = json.data
	save_data = data

	# 只有非自动保存槽位才更新当前槽位
	if slot != AUTOSAVE_SLOT:
		current_save_slot = slot
		_logger("Current save slot updated to: %d" % slot)
	else:
		# 自动保存加载时临时设置，但不改变用户的主要槽位
		_logger("Loaded from autosave, current slot unchanged: %d" % current_save_slot)

	# 应用存档数据
	_apply_save_data(data)

	_logger("Game loaded from slot: %d" % slot)
	load_completed.emit(slot, true)
	return true

## 应用存档数据到游戏状态
func _apply_save_data(loaded_data: Dictionary):
	# 首先检查数据完整性
	_logger("Applying save data. Has player_data: %s, Has position: %s" % [
		loaded_data.has("player_data"), 
		loaded_data.has("player_data") and loaded_data.player_data.has("position")
	])
	
	# 恢复玩家数据
	if loaded_data.has("player_data") and GameManager:
		GameManager.set_player_data(loaded_data.player_data)
		
		# 恢复背包数据
		if loaded_data.player_data.has("inventory") and InventoryManager:
			InventoryManager.set_inventory_data(loaded_data.player_data.inventory)
	
	# 恢复场景
	if loaded_data.has("metadata") and loaded_data.metadata.has("location"):
		var scene_path = loaded_data.metadata.location
		if ResourceLoader.exists(scene_path):
			SceneManager.fade_to_scene(scene_path)
			
			# 连接场景切换信号，在场景切换完成时恢复玩家位置
			if SceneManager and not SceneManager.scene_changed.is_connected(_on_scene_changed_for_position_restore):
				SceneManager.scene_changed.connect(_on_scene_changed_for_position_restore.bind(loaded_data), CONNECT_ONE_SHOT)
	
	# 恢复游戏状态
	if loaded_data.has("game_state"):
		var game_state = loaded_data.game_state
		print(game_state)
		# 可以在这里恢复更多状态
		# 例如：玩家数据、世界状态等
	
	# 启动自动保存
	if autosave_enabled:
		start_autosave()

## 场景切换完成后恢复玩家位置回调
func _on_scene_changed_for_position_restore(old_scene_path: String, new_scene_path: String, loaded_save_data: Dictionary):
	_logger("Scene changed for position restore: %s -> %s" % [old_scene_path, new_scene_path])
	
	# 检查是否切换到了正确的场景
	if loaded_save_data.has("player_data") and loaded_save_data.player_data.has("position"):
		var position_data = loaded_save_data.player_data.position
		if position_data.has("scene_path") and new_scene_path == position_data.scene_path:
			_apply_player_position(loaded_save_data)
		else:
			_logger("Scene path mismatch. Expected: %s, Got: %s" % [position_data.get("scene_path", "unknown"), new_scene_path])

## 自动保存（保存到槽位0）
func autosave() -> bool:
	var success = save_game(AUTOSAVE_SLOT)
	if success:
		_logger("Autosave completed")
	else:
		_logger("Autosave failed")
	autosave_completed.emit(success)
	return success

## 删除存档
func delete_save(slot: int) -> bool:
	if slot < 0 or slot >= MAX_SAVE_SLOTS:
		_logger("Invalid save slot: %d" % slot)
		return false
	
	var file_path = _get_save_file_path(slot)
	if not FileAccess.file_exists(file_path):
		_logger("Save does not exist, slot: %d" % slot)
		return false
	
	DirAccess.open("user://").remove(file_path)
	_logger("Save deleted, slot: %d" % slot)
	return true

## 复制存档
func copy_save(from_slot: int, to_slot: int) -> bool:
	if not has_save(from_slot):
		_logger("Source save does not exist, slot: %d" % from_slot)
		return false
	
	if to_slot < 0 or to_slot >= MAX_SAVE_SLOTS:
		_logger("Invalid target slot: %d" % to_slot)
		return false
	
	var source_file = FileAccess.open(_get_save_file_path(from_slot), FileAccess.READ)
	if source_file == null:
		return false
	
	var data_string = source_file.get_as_text()
	source_file.close()
	
	# 解析并更新槽位信息
	var json = JSON.new()
	var parse_result = json.parse(data_string)
	if parse_result == OK:
		var data = json.data
		if data.has("metadata"):
			data.metadata.slot = to_slot
			data.metadata.save_time = Time.get_datetime_string_from_system()
		data_string = JSON.stringify(data, "\t")
	
	var target_file = FileAccess.open(_get_save_file_path(to_slot), FileAccess.WRITE)
	if target_file == null:
		return false
	
	target_file.store_string(data_string)
	target_file.close()
	
	_logger("Save copied from slot %d to slot %d" % [from_slot, to_slot])
	return true

## 快速保存（使用当前槽位或槽位1）
## 注意：这个函数不考虑GameManager的intended_save_slot逻辑
## 建议使用GameManager.get_save_slot_for_current_game()来获取正确的槽位
func quick_save() -> bool:
	var slot = current_save_slot if current_save_slot > 0 else 1
	_logger("Quick save to slot: %d" % slot)
	return save_game(slot)

## 快速加载（从最近的存档）
func quick_load() -> bool:
	# 寻找最新的存档
	var latest_slot = -1
	var latest_time = ""

	for i in range(1, MAX_SAVE_SLOTS):  # 跳过自动保存槽位0
		var info = get_save_info(i)
		if not info.is_empty():
			if latest_time.is_empty() or info.save_time > latest_time:
				latest_time = info.save_time
				latest_slot = i

	if latest_slot == -1:
		_logger("No loadable save found")
		return false

	return load_game(latest_slot)

## 重新加载当前存档槽位
func reload_current_save() -> bool:
	if current_save_slot <= 0:
		_logger("No valid save slot to reload")
		return false

	if not has_save(current_save_slot):
		_logger("Save slot %d does not exist, clearing current slot" % current_save_slot)
		current_save_slot = -1  # 清除无效的当前槽位
		return false

	_logger("Reloading save from slot: %d" % current_save_slot)
	return load_game(current_save_slot)

## 设置当前存档槽位
func set_current_save_slot(slot: int):
	if slot == -1:
		# 清除当前存档槽位
		current_save_slot = -1
		_logger("Current save slot cleared")
	elif slot >= 0 and slot < MAX_SAVE_SLOTS:
		# 只有非自动保存槽位才能设置为当前槽位
		if slot != AUTOSAVE_SLOT:
			current_save_slot = slot
			_logger("Current save slot set to: %d" % slot)
		else:
			_logger("Cannot set autosave slot as current save slot")
	else:
		_logger("Invalid save slot: %d" % slot)

## 获取当前存档槽位
func get_current_save_slot() -> int:
	return current_save_slot

## 获取当前存档数据
func get_save_data() -> Dictionary:
	return save_data

## 检查是否有自动保存
func has_autosave() -> bool:
	return has_save(AUTOSAVE_SLOT)

## 从自动保存加载
func load_autosave() -> bool:
	return load_game(AUTOSAVE_SLOT)

## 统一日志输出函数
func _logger(msg: String) -> void:
	print("[SaveManager] %s" % msg)

# ================================
# 设置管理功能 (独立于存档系统)
# ================================

## 加载设置文件
func _load_settings():
	settings_config = ConfigFile.new()
	
	# 如果设置文件不存在，创建默认设置
	if not FileAccess.file_exists(SETTINGS_FILE_PATH):
		_create_default_settings()
		return
	
	var err = settings_config.load(SETTINGS_FILE_PATH)
	if err != OK:
		_logger("Failed to load settings file, using defaults")
		_create_default_settings()
		return
	
	# 加载设置到内存
	_sync_settings_from_file()
	_logger("Settings loaded successfully")

## 创建默认设置
func _create_default_settings():
	settings_config = ConfigFile.new()
	
	# 默认游戏设置
	settings_config.set_value(DEFAULT_SETTINGS_SECTION, "master_volume", 1.0)
	settings_config.set_value(DEFAULT_SETTINGS_SECTION, "music_volume", 0.8)
	settings_config.set_value(DEFAULT_SETTINGS_SECTION, "sfx_volume", 0.8)
	settings_config.set_value(DEFAULT_SETTINGS_SECTION, "fullscreen", false)
	settings_config.set_value(DEFAULT_SETTINGS_SECTION, "vsync", true)
	settings_config.set_value(DEFAULT_SETTINGS_SECTION, "language", "zh_CN")
	settings_config.set_value(DEFAULT_SETTINGS_SECTION, "autosave_enabled", false)
	settings_config.set_value(DEFAULT_SETTINGS_SECTION, "autosave_interval", 120.0)
	
	# 默认控制设置
	settings_config.set_value("controls", "move_up", "w")
	settings_config.set_value("controls", "move_down", "s") 
	settings_config.set_value("controls", "move_left", "a")
	settings_config.set_value("controls", "move_right", "d")
	settings_config.set_value("controls", "action", "f")
	settings_config.set_value("controls", "menu", "escape")
	
	# 默认显示设置
	settings_config.set_value("display", "resolution_width", 1920)
	settings_config.set_value("display", "resolution_height", 1080)
	settings_config.set_value("display", "window_mode", 0)  # 0=窗口, 1=最小化窗口, 2=最大化窗口, 3=全屏, 4=独占全屏
	settings_config.set_value("display", "borderless", false)  # 无边框窗口标志
	
	_sync_settings_from_file()
	_save_settings()
	_logger("Default settings created")

## 同步设置从文件到内存
func _sync_settings_from_file():
	settings_data.clear()
	
	for section in settings_config.get_sections():
		settings_data[section] = {}
		for key in settings_config.get_section_keys(section):
			settings_data[section][key] = settings_config.get_value(section, key)

## 保存设置到文件
func _save_settings() -> bool:
	var err = settings_config.save(SETTINGS_FILE_PATH)
	if err != OK:
		_logger("Failed to save settings file")
		return false
	
	_logger("Settings saved successfully")
	return true

## 获取设置值
func get_setting(key: String, section: String = DEFAULT_SETTINGS_SECTION) -> Variant:
	if not settings_data.has(section):
		_logger("Settings section not found: %s" % section)
		return null
	
	if not settings_data[section].has(key):
		_logger("Settings key not found: %s.%s" % [section, key])
		return null
	
	return settings_data[section][key]

## 设置值
func set_setting(key: String, value: Variant, section: String = DEFAULT_SETTINGS_SECTION) -> bool:
	# 更新内存中的设置
	if not settings_data.has(section):
		settings_data[section] = {}
	
	var old_value = settings_data[section].get(key, null)
	
	# 检查值是否真正发生变化
	if old_value == value:
		# 值没有变化，直接返回成功，不执行保存和信号发送
		return true
	
	settings_data[section][key] = value
	
	# 更新配置文件对象
	settings_config.set_value(section, key, value)
	
	# 保存到文件
	if _save_settings():
		# 发送设置改变信号
		settings_changed.emit(key, value)
		_logger("Setting updated: %s.%s = %s" % [section, key, str(value)])
		return true
	else:
		# 保存失败，回滚内存中的值
		if old_value != null:
			settings_data[section][key] = old_value
		else:
			settings_data[section].erase(key)
		return false

## 获取所有设置
func get_all_settings() -> Dictionary:
	return settings_data.duplicate(true)

## 获取指定分组的设置
func get_settings_section(section: String) -> Dictionary:
	if settings_data.has(section):
		return settings_data[section].duplicate(true)
	return {}

## 重置设置到默认值
func reset_settings() -> bool:
	_create_default_settings()
	_logger("Settings reset to defaults")
	return true

## 重置指定分组的设置
func reset_settings_section(section: String) -> bool:
	if not settings_data.has(section):
		_logger("Settings section not found: %s" % section)
		return false
	
	# 移除该分组的所有设置
	for key in settings_data[section].keys():
		settings_config.erase_section_key(section, key)
	
	settings_data.erase(section)
	
	# 如果是默认分组，重新创建默认值
	if section == DEFAULT_SETTINGS_SECTION:
		_create_default_settings()
	else:
		_save_settings()
	
	_logger("Settings section reset: %s" % section)
	return true

## 检查设置是否存在
func has_setting(key: String, section: String = DEFAULT_SETTINGS_SECTION) -> bool:
	return settings_data.has(section) and settings_data[section].has(key)

## 便捷方法：获取常用设置
func get_master_volume() -> float:
	return get_setting("master_volume", DEFAULT_SETTINGS_SECTION)

func set_master_volume(volume: float) -> bool:
	return set_setting("master_volume", clamp(volume, 0.0, 1.0), DEFAULT_SETTINGS_SECTION)

func get_music_volume() -> float:
	return get_setting("music_volume", DEFAULT_SETTINGS_SECTION)

func set_music_volume(volume: float) -> bool:
	return set_setting("music_volume", clamp(volume, 0.0, 1.0), DEFAULT_SETTINGS_SECTION)

func get_sfx_volume() -> float:
	return get_setting("sfx_volume", DEFAULT_SETTINGS_SECTION)

func set_sfx_volume(volume: float) -> bool:
	return set_setting("sfx_volume", clamp(volume, 0.0, 1.0), DEFAULT_SETTINGS_SECTION)

func is_fullscreen() -> bool:
	return get_setting("fullscreen", DEFAULT_SETTINGS_SECTION)

func set_fullscreen(enabled: bool) -> bool:
	return set_setting("fullscreen", enabled, DEFAULT_SETTINGS_SECTION)

func is_vsync_enabled() -> bool:
	return get_setting("vsync", DEFAULT_SETTINGS_SECTION)

func set_vsync_enabled(enabled: bool) -> bool:
	return set_setting("vsync", enabled, DEFAULT_SETTINGS_SECTION)

func get_language() -> String:
	return get_setting("language", DEFAULT_SETTINGS_SECTION)

func set_language(lang: String) -> bool:
	return set_setting("language", lang, DEFAULT_SETTINGS_SECTION)

func is_borderless() -> bool:
	return get_setting("borderless", "display")

func set_borderless(enabled: bool) -> bool:
	return set_setting("borderless", enabled, "display")

func get_window_mode() -> int:
	return get_setting("window_mode", "display")

func set_window_mode(mode: int) -> bool:
	return set_setting("window_mode", mode, "display")

## 从设置文件同步自动保存设置
func sync_autosave_settings():
	var enabled = get_setting("autosave_enabled", DEFAULT_SETTINGS_SECTION)
	var interval = get_setting("autosave_interval", DEFAULT_SETTINGS_SECTION)
	
	if enabled != null:
		set_autosave_enabled(enabled)
	if interval != null:
		set_autosave_interval(interval)

## 保存自动保存设置到配置文件
func save_autosave_settings():
	set_setting("autosave_enabled", autosave_enabled, DEFAULT_SETTINGS_SECTION)
	set_setting("autosave_interval", autosave_interval, DEFAULT_SETTINGS_SECTION)

# ================================
# 传送位置管理功能 (临时存储玩家传送坐标)
# ================================

## 设置传送位置
func set_teleport_position(position: Vector2):
	teleport_position = position
	_logger("Teleport position set to: %s" % position)

## 获取传送位置
func get_teleport_position() -> Vector2:
	_logger("Teleport position retrieved: %s" % teleport_position)
	return teleport_position

## 清除传送位置
func clear_teleport_position():
	teleport_position = Vector2.ZERO
	_logger("Teleport position cleared")
