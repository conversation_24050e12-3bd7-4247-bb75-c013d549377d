extends Node

## 顶层应用状态机 - 管理程序级状态（菜单、游戏内、暂停等）
## 对应状态机设计图的第一层状态机

## 应用状态枚举
enum AppState {
	MAIN_SCENE,   ## 主场景（游戏最开始的状态，动画播放阶段）
	MAIN_MENU,    ## 主菜单
	IN_GAME,      ## 游戏中
	PAUSED,       ## 暂停
	SETTINGS,     ## 设置界面
	INVENTORY,    ## 背包界面
	GAME_OVER,    ## 游戏结束
	QUIT_APP      ## 退出应用
}

## 当前应用状态
var current_state: AppState = AppState.MAIN_SCENE

## 进入设置前的状态（用于从设置返回）
var previous_state_before_settings: AppState = AppState.MAIN_MENU

## 当前要转换到的目标状态（用于在_exit_state中判断）
var target_state: AppState = AppState.MAIN_SCENE

## 状态改变信号 - 其他系统可以监听这个信号来响应状态变化
signal state_changed(old_state: AppState, new_state: AppState)

## 游戏内状态管理器引用
var gameplay_state_manager: Node = null

func _ready():
	_logger("App State Manager Ready!")
	# 等待一帧确保其他autoload都已经初始化
	await get_tree().process_frame
	gameplay_state_manager = get_node("/root/GameplayStateManager")

## 状态转换函数
func change_state(new_state: AppState) -> void:
	var old_state = current_state
	target_state = new_state  # 设置目标状态供_exit_state使用
	
	# 状态转换验证
	if not _is_valid_transition(old_state, new_state):
		push_warning("Invalid state transition from %s to %s" % [AppState.keys()[old_state], AppState.keys()[new_state]])
		return
	
	_logger("State: %s -> %s" % [AppState.keys()[old_state], AppState.keys()[new_state]])
	
	# 退出当前状态
	_exit_state(old_state)
	
	# 更新状态
	current_state = new_state
	
	# 进入新状态
	_enter_state(new_state)
	
	# 发送状态改变信号
	state_changed.emit(old_state, new_state)

## 验证状态转换是否合法
func _is_valid_transition(from_state: AppState, to_state: AppState) -> bool:
	match from_state:
		AppState.MAIN_SCENE:
			return to_state in [AppState.MAIN_MENU, AppState.QUIT_APP]
		AppState.MAIN_MENU:
			return to_state in [AppState.IN_GAME, AppState.SETTINGS, AppState.QUIT_APP]
		AppState.IN_GAME:
			return to_state in [AppState.PAUSED, AppState.INVENTORY, AppState.GAME_OVER, AppState.QUIT_APP]
		AppState.PAUSED:
			return to_state in [AppState.IN_GAME, AppState.SETTINGS, AppState.MAIN_MENU, AppState.QUIT_APP]
		AppState.SETTINGS:
			return to_state in [AppState.MAIN_MENU, AppState.PAUSED, AppState.IN_GAME]
		AppState.INVENTORY:
			return to_state in [AppState.IN_GAME]
		AppState.GAME_OVER:
			return to_state in [AppState.MAIN_MENU, AppState.IN_GAME]
		AppState.QUIT_APP:
			return false  # 退出状态不能转换到其他状态
	return false

## 退出状态时的处理
func _exit_state(state: AppState) -> void:
	match state:
		AppState.IN_GAME:
			# 停止游戏内状态机
			if gameplay_state_manager:
				gameplay_state_manager.stop_gameplay()
		AppState.PAUSED:
			# 只有在不是转到设置时才取消暂停
			if target_state != AppState.SETTINGS:
				# 取消暂停
				get_tree().paused = false
			# 清理暂停菜单 - 现在直接使用SceneManager
			if SceneManager:
				SceneManager.cleanup_pause_menu_for_state_change()
		AppState.SETTINGS:
			# 统一使用UI层隐藏方式
			SceneManager.hide_settings_ui()
		AppState.INVENTORY:
			# 隐藏背包UI
			SceneManager.hide_inventory_ui()

## 进入状态时的处理
func _enter_state(state: AppState) -> void:
	match state:
		AppState.MAIN_SCENE:
			# 主场景状态，通常不需要特殊处理
			# 主场景应该已经在运行动画
			pass
		AppState.MAIN_MENU:
			# 跳转到主菜单场景
			SceneManager.go_to_main_menu()
		AppState.IN_GAME:
			# 启动游戏内状态机
			if gameplay_state_manager:
				gameplay_state_manager.start_gameplay()
			get_tree().paused = false
		AppState.PAUSED:
			# 暂停游戏
			get_tree().paused = true
			# 显示暂停菜单 - 现在直接使用SceneManager
			if SceneManager:
				SceneManager.show_pause_menu_for_state_change()
		AppState.SETTINGS:
			# 统一使用UI层覆盖方式显示设置
			SceneManager.show_settings_ui()
			# 只有从暂停菜单进入时才保持游戏暂停
			if previous_state_before_settings == AppState.PAUSED:
				get_tree().paused = true
		AppState.INVENTORY:
			# 显示背包UI并暂停游戏
			get_tree().paused = true
			SceneManager.show_inventory_ui()
		AppState.GAME_OVER:
			# 显示游戏结束界面
			pass
		AppState.QUIT_APP:
			# 退出应用
			get_tree().quit()

## 便捷的状态转换方法
func go_to_main_menu() -> void:
	change_state(AppState.MAIN_MENU)

func start_game() -> void:
	change_state(AppState.IN_GAME)

func continue_game() -> void:
	change_state(AppState.IN_GAME)

func pause_game() -> void:
	change_state(AppState.PAUSED)

func resume_game() -> void:
	change_state(AppState.IN_GAME)

func open_settings() -> void:
	# 记住进入设置前的状态
	previous_state_before_settings = current_state
	change_state(AppState.SETTINGS)

func open_inventory() -> void:
	change_state(AppState.INVENTORY)

func back_from_settings() -> void:
	# 根据进入设置前的状态返回
	if current_state == AppState.SETTINGS:
		change_state(previous_state_before_settings)
	else:
		_logger("Warning: back_from_settings called when not in SETTINGS state")

func back_from_inventory() -> void:
	if current_state == AppState.INVENTORY:
		change_state(AppState.IN_GAME)
	else:
		_logger("Warning: back_from_inventory called when not in INVENTORY state")

func quit_to_menu() -> void:
	change_state(AppState.MAIN_MENU)

func game_over() -> void:
	change_state(AppState.GAME_OVER)

func retry_game() -> void:
	change_state(AppState.IN_GAME)

func quit_application() -> void:
	change_state(AppState.QUIT_APP)


## 获取当前状态
func get_current_state() -> AppState:
	return current_state

## 获取当前状态名称
func get_current_state_name() -> String:
	return AppState.keys()[current_state]

## 判断是否在游戏中
func is_in_game() -> bool:
	return current_state == AppState.IN_GAME

## 判断是否暂停
func is_paused() -> bool:
	return current_state == AppState.PAUSED

## 统一日志输出函数
func _logger(msg: String) -> void:
	print("[AppStateManager] %s" % msg)
