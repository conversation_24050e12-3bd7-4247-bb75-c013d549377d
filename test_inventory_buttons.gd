extends Node
## 测试脚本：验证背包按钮功能

func _ready():
	print("=== 背包按钮功能测试 ===")
	
	# 等待一帧确保所有节点都已初始化
	await get_tree().process_frame
	
	test_inventory_manager()
	test_inventory_ui()

func test_inventory_manager():
	print("\n--- 测试 InventoryManager ---")
	
	if not InventoryManager:
		print("❌ InventoryManager 未找到")
		return
	
	print("✅ InventoryManager 已加载")
	
	# 测试添加物品
	var success = InventoryManager.add_item("health_potion", 3)
	print("添加血瓶结果: %s" % success)
	
	# 测试添加装备
	success = InventoryManager.add_item("iron_sword", 1)
	print("添加铁剑结果: %s" % success)
	
	# 打印背包状态
	var slots = InventoryManager.get_inventory_slots()
	print("背包槽位数量: %d" % slots.size())
	
	for i in range(slots.size()):
		var slot = slots[i]
		if not slot.is_empty():
			var template = slot.get_template()
			print("槽位 %d: %s x%d" % [i, template.name, slot.get_quantity()])

func test_inventory_ui():
	print("\n--- 测试 InventoryUI ---")
	
	# 查找 InventoryUI 节点
	var inventory_ui = find_inventory_ui_node()
	if not inventory_ui:
		print("❌ InventoryUI 节点未找到")
		return
	
	print("✅ InventoryUI 节点已找到")
	
	# 检查按钮是否存在
	var use_button = inventory_ui.get_node_or_null("%UseButton")
	var drop_button = inventory_ui.get_node_or_null("%DropButton")
	
	if use_button:
		print("✅ 使用按钮已找到，禁用状态: %s" % use_button.disabled)
	else:
		print("❌ 使用按钮未找到")
	
	if drop_button:
		print("✅ 丢弃按钮已找到，禁用状态: %s" % drop_button.disabled)
	else:
		print("❌ 丢弃按钮未找到")
	
	# 测试显示背包
	if inventory_ui.has_method("show_inventory"):
		inventory_ui.show_inventory()
		print("✅ 背包界面已显示")
	
	# 等待一帧让UI更新
	await get_tree().process_frame
	
	# 模拟选择第一个有物品的槽位
	var slots = InventoryManager.get_inventory_slots()
	for i in range(slots.size()):
		if not slots[i].is_empty():
			print("模拟选择槽位 %d" % i)
			if inventory_ui.has_method("_on_inventory_slot_pressed"):
				inventory_ui._on_inventory_slot_pressed(i)
			break
	
	# 再次检查按钮状态
	await get_tree().process_frame
	if use_button:
		print("选择物品后 - 使用按钮禁用状态: %s" % use_button.disabled)
	if drop_button:
		print("选择物品后 - 丢弃按钮禁用状态: %s" % drop_button.disabled)

func find_inventory_ui_node() -> Node:
	# 在场景树中查找 InventoryUI 节点
	var root = get_tree().current_scene
	return find_node_by_name(root, "InventoryUI")

func find_node_by_name(node: Node, name: String) -> Node:
	if node.name == name:
		return node
	
	for child in node.get_children():
		var result = find_node_by_name(child, name)
		if result:
			return result
	
	return null

func _input(event):
	if event.is_action_pressed("ui_accept"):
		print("\n=== 重新运行测试 ===")
		test_inventory_manager()
		test_inventory_ui()
