# Wake 游戏状态机系统使用文档

## 概述

本文档详细介绍了 Wake 项目中实现的双层状态机系统。该系统将游戏状态分为两个层级：
- **程序级状态机 (AppStateManager)**: 管理应用程序级别的状态（菜单、游戏、暂停等）
- **游戏内状态机 (GameplayStateManager)**: 管理游戏内循环状态（现实世界、梦境世界、战斗等）

## 系统架构

### 设计原理

状态机采用分层设计，将不同层级的状态逻辑分离：

```
程序级状态机 (AppStateManager)
├── MainMenu     # 主菜单
├── InGame       # 游戏中 ──┐
├── Paused       # 暂停     │
├── Settings     # 设置     │
├── GameOver     # 游戏结束  │
└── QuitApp      # 退出应用  │
                            │
                 游戏内状态机 (GameplayStateManager)
                 ├── RealWorld    # 现实世界
                 │   ├── Day      # 白天
                 │   ├── Night    # 夜晚
                 │   └── BossDay  # Boss日
                 ├── DreamWorld   # 梦境世界
                 ├── Battle       # 普通战斗
                 ├── BossBattle   # Boss战斗
                 └── ChapterEnd   # 章节结束
```

### 状态转换图

根据 `Doc/状态机.mmd` 设计：

**程序级状态转换:**
```
[*] → MainMenu
MainMenu → InGame (开始游戏)
MainMenu → Settings (打开设置)
InGame → Paused (按暂停键)
Paused → InGame (恢复游戏)
Paused → Settings (打开设置)
Paused → MainMenu (退出到菜单)
InGame → GameOver (玩家失败)
GameOver → MainMenu (返回菜单)
GameOver → InGame (重试)
```

**游戏内状态转换:**
```
[*] → RealWorld
RealWorld → DreamWorld (入睡)
DreamWorld → Battle (遇到敌人)
Battle → DreamWorld (战斗胜利)
Battle → BossBattle (触发Boss)
BossBattle → DreamWorld (Boss战胜利)
DreamWorld → ChapterEnd (完成目标)
ChapterEnd → RealWorld (醒来)

RealWorld 子状态:
Day → Night (压力达到最大)
Night → BossDay (进入Boss门)
BossDay → Day (Boss被击败)
```

## API 参考

### AppStateManager (程序级状态机)

#### 枚举类型
```gdscript
enum AppState {
    MAIN_MENU,    # 主菜单
    IN_GAME,      # 游戏中
    PAUSED,       # 暂停
    SETTINGS,     # 设置界面
    GAME_OVER,    # 游戏结束
    QUIT_APP      # 退出应用
}
```

#### 主要方法

**状态控制方法:**
```gdscript
# 基础状态转换
AppStateManager.change_state(new_state: AppState) -> void

# 便捷方法
AppStateManager.start_game() -> void           # 开始游戏
AppStateManager.continue_game() -> void        # 继续游戏
AppStateManager.pause_game() -> void           # 暂停游戏
AppStateManager.resume_game() -> void          # 恢复游戏
AppStateManager.open_settings() -> void        # 打开设置
AppStateManager.back_from_settings() -> void   # 从设置返回
AppStateManager.quit_to_menu() -> void         # 退出到菜单
AppStateManager.game_over() -> void            # 游戏结束
AppStateManager.retry_game() -> void           # 重试游戏
AppStateManager.quit_application() -> void     # 退出应用
```

**状态查询方法:**
```gdscript
AppStateManager.get_current_state() -> AppState        # 获取当前状态
AppStateManager.get_current_state_name() -> String     # 获取状态名称
AppStateManager.is_in_game() -> bool                   # 是否在游戏中
AppStateManager.is_paused() -> bool                    # 是否暂停
```

#### 信号

```gdscript
signal state_changed(old_state: AppState, new_state: AppState)
```

**使用示例:**
```gdscript
# 监听状态变化
AppStateManager.state_changed.connect(_on_app_state_changed)

func _on_app_state_changed(old_state, new_state):
    print("应用状态从 %s 变为 %s" % [
        AppStateManager.AppState.keys()[old_state],
        AppStateManager.AppState.keys()[new_state]
    ])
```

### GameplayStateManager (游戏内状态机)

#### 枚举类型
```gdscript
enum GameplayState {
    IDLE,           # 空闲状态
    REAL_WORLD,     # 现实世界状态
    DREAM_WORLD,    # 梦境世界状态
    BATTLE,         # 普通战斗
    BOSS_BATTLE,    # Boss战斗
    CHAPTER_END     # 章节结束
}

enum RealWorldState {
    DAY,        # 白天
    NIGHT,      # 夜晚
    BOSS_DAY    # Boss日
}
```

#### 主要方法

**生命周期管理:**
```gdscript
GameplayStateManager.start_gameplay() -> void  # 启动游戏循环（由AppStateManager自动调用）
GameplayStateManager.stop_gameplay() -> void   # 停止游戏循环（由AppStateManager自动调用）
```

**状态转换方法:**
```gdscript
# 基础状态转换
GameplayStateManager.change_state(new_state: GameplayState) -> void
GameplayStateManager.change_real_world_state(new_state: RealWorldState) -> void
```

**游戏事件触发方法:**
```gdscript
# 现实世界事件
GameplayStateManager.pressure_maxed() -> void      # 压力值达到最大 (Day → Night)
GameplayStateManager.enter_boss_gate() -> void     # 进入Boss门 (Night → BossDay)
GameplayStateManager.boss_defeated() -> void       # Boss被击败 (BossDay → Day)

# 世界切换事件
GameplayStateManager.fall_asleep() -> void         # 入睡 (RealWorld → DreamWorld)
GameplayStateManager.wake_up() -> void             # 醒来 (ChapterEnd → RealWorld)

# 战斗事件
GameplayStateManager.encounter_enemy() -> void     # 遇到敌人 (DreamWorld → Battle)
GameplayStateManager.win_battle() -> void          # 战斗胜利 (Battle → DreamWorld)
GameplayStateManager.trigger_boss_battle() -> void # 触发Boss战 (Battle → BossBattle)
GameplayStateManager.boss_battle_victory() -> void # Boss战胜利 (BossBattle → DreamWorld)

# 章节事件
GameplayStateManager.objectives_complete() -> void # 完成目标 (DreamWorld → ChapterEnd)

# 失败事件
GameplayStateManager.trigger_player_defeated() -> void # 玩家失败（触发GameOver）
```

**状态查询方法:**
```gdscript
# 基本状态查询
GameplayStateManager.get_current_state() -> GameplayState
GameplayStateManager.get_current_real_world_state() -> RealWorldState
GameplayStateManager.get_current_state_name() -> String
GameplayStateManager.get_current_real_world_state_name() -> String

# 便捷状态判断
GameplayStateManager.is_in_real_world() -> bool    # 是否在现实世界
GameplayStateManager.is_in_dream_world() -> bool   # 是否在梦境世界
GameplayStateManager.is_in_battle() -> bool        # 是否在战斗中
GameplayStateManager.is_day_time() -> bool         # 是否是白天
GameplayStateManager.is_night_time() -> bool       # 是否是夜晚
GameplayStateManager.is_boss_day() -> bool         # 是否是Boss日
```

#### 信号

```gdscript
signal gameplay_state_changed(old_state: GameplayState, new_state: GameplayState)
signal real_world_state_changed(old_state: RealWorldState, new_state: RealWorldState)
signal player_defeated()  # 玩家失败信号
```

## 使用示例

### 基本使用流程

#### 1. 游戏启动和菜单操作

```gdscript
# 在主菜单脚本中
extends Control

func _on_start_button_pressed():
    # 开始新游戏
    AppStateManager.start_game()

func _on_continue_button_pressed():
    # 继续游戏
    AppStateManager.continue_game()

func _on_settings_button_pressed():
    # 打开设置
    AppStateManager.open_settings()

func _on_quit_button_pressed():
    # 退出游戏
    AppStateManager.quit_application()
```

#### 2. 游戏内状态管理

```gdscript
# 在游戏主控制器脚本中
extends Node

var pressure_level: float = 0.0
var max_pressure: float = 100.0

func _ready():
    # 监听游戏状态变化
    GameplayStateManager.gameplay_state_changed.connect(_on_gameplay_state_changed)
    GameplayStateManager.real_world_state_changed.connect(_on_real_world_state_changed)

func _process(delta):
    # 只在现实世界的白天增加压力值
    if GameplayStateManager.is_day_time():
        pressure_level += delta * 10.0
        if pressure_level >= max_pressure:
            # 压力达到最大，转入夜晚
            GameplayStateManager.pressure_maxed()
            pressure_level = 0.0

func _on_sleep_action():
    # 玩家选择睡觉
    if GameplayStateManager.is_in_real_world():
        GameplayStateManager.fall_asleep()

func _on_boss_gate_interaction():
    # 玩家与Boss门交互
    if GameplayStateManager.is_night_time():
        GameplayStateManager.enter_boss_gate()

func _on_gameplay_state_changed(old_state, new_state):
    match new_state:
        GameplayStateManager.GameplayState.REAL_WORLD:
            print("进入现实世界")
            # 加载现实世界场景、UI等
        GameplayStateManager.GameplayState.DREAM_WORLD:
            print("进入梦境世界")
            # 加载梦境世界场景、初始化梦境逻辑
        GameplayStateManager.GameplayState.BATTLE:
            print("进入战斗")
            # 初始化战斗系统
        GameplayStateManager.GameplayState.CHAPTER_END:
            print("章节结束")
            # 显示章节总结界面

func _on_real_world_state_changed(old_state, new_state):
    match new_state:
        GameplayStateManager.RealWorldState.DAY:
            print("现在是白天")
            # 设置白天光照、音效等
        GameplayStateManager.RealWorldState.NIGHT:
            print("现在是夜晚")
            # 设置夜晚光照、音效等
        GameplayStateManager.RealWorldState.BOSS_DAY:
            print("Boss日来临")
            # 设置特殊的Boss日环境
```

#### 3. 战斗系统集成

```gdscript
# 在战斗控制器脚本中
extends Node

var enemy_health: int = 100
var player_health: int = 100

func _ready():
    # 只在战斗状态下运行战斗逻辑
    if not GameplayStateManager.is_in_battle():
        set_process(false)
        return

func _on_enemy_defeated():
    if GameplayStateManager.get_current_state() == GameplayStateManager.GameplayState.BATTLE:
        # 普通战斗胜利
        GameplayStateManager.win_battle()
    elif GameplayStateManager.get_current_state() == GameplayStateManager.GameplayState.BOSS_BATTLE:
        # Boss战斗胜利
        GameplayStateManager.boss_battle_victory()

func _on_player_defeated():
    # 玩家失败
    GameplayStateManager.trigger_player_defeated()

func _on_boss_trigger():
    # 在普通战斗中触发Boss
    if GameplayStateManager.get_current_state() == GameplayStateManager.GameplayState.BATTLE:
        GameplayStateManager.trigger_boss_battle()
```

#### 4. 暂停系统

```gdscript
# 暂停系统已内置在AppStateManager中
# ESC键自动处理暂停/恢复

# 如果需要自定义暂停菜单
extends Control

func _ready():
    # 监听暂停状态
    AppStateManager.state_changed.connect(_on_app_state_changed)

func _on_app_state_changed(old_state, new_state):
    if new_state == AppStateManager.AppState.PAUSED:
        # 显示暂停菜单
        show()
    else:
        # 隐藏暂停菜单
        hide()

func _on_resume_button_pressed():
    AppStateManager.resume_game()

func _on_settings_button_pressed():
    AppStateManager.open_settings()

func _on_main_menu_button_pressed():
    AppStateManager.quit_to_menu()
```

### 高级使用场景

#### 状态驱动的UI管理

```gdscript
# UI管理器
extends CanvasLayer

@onready var main_menu_ui = $MainMenuUI
@onready var game_ui = $GameUI
@onready var pause_ui = $PauseUI
@onready var day_ui = $DayUI
@onready var night_ui = $NightUI
@onready var battle_ui = $BattleUI

func _ready():
    AppStateManager.state_changed.connect(_on_app_state_changed)
    GameplayStateManager.gameplay_state_changed.connect(_on_gameplay_state_changed)
    GameplayStateManager.real_world_state_changed.connect(_on_real_world_state_changed)

func _on_app_state_changed(old_state, new_state):
    # 隐藏所有UI
    hide_all_ui()
    
    # 根据状态显示对应UI
    match new_state:
        AppStateManager.AppState.MAIN_MENU:
            main_menu_ui.show()
        AppStateManager.AppState.IN_GAME:
            game_ui.show()
        AppStateManager.AppState.PAUSED:
            pause_ui.show()

func _on_gameplay_state_changed(old_state, new_state):
    if not AppStateManager.is_in_game():
        return
    
    match new_state:
        GameplayStateManager.GameplayState.BATTLE, GameplayStateManager.GameplayState.BOSS_BATTLE:
            battle_ui.show()
        _:
            battle_ui.hide()

func _on_real_world_state_changed(old_state, new_state):
    if not GameplayStateManager.is_in_real_world():
        return
    
    match new_state:
        GameplayStateManager.RealWorldState.DAY:
            day_ui.show()
            night_ui.hide()
        GameplayStateManager.RealWorldState.NIGHT, GameplayStateManager.RealWorldState.BOSS_DAY:
            day_ui.hide()
            night_ui.show()

func hide_all_ui():
    for child in get_children():
        child.hide()
```

#### 存档系统集成

```gdscript
# 存档管理器
extends Node

func save_game():
    var save_data = {
        "app_state": AppStateManager.get_current_state(),
        "gameplay_state": GameplayStateManager.get_current_state(),
        "real_world_state": GameplayStateManager.get_current_real_world_state(),
        # 其他游戏数据...
    }
    
    var file = FileAccess.open("user://savegame.dat", FileAccess.WRITE)
    file.store_string(JSON.stringify(save_data))
    file.close()

func load_game():
    if not FileAccess.file_exists("user://savegame.dat"):
        return false
    
    var file = FileAccess.open("user://savegame.dat", FileAccess.READ)
    var save_data = JSON.parse_string(file.get_as_text())
    file.close()
    
    # 恢复状态
    AppStateManager.change_state(save_data.app_state)
    await get_tree().process_frame  # 等待状态机初始化
    
    if save_data.app_state == AppStateManager.AppState.IN_GAME:
        GameplayStateManager.change_state(save_data.gameplay_state)
        if save_data.gameplay_state == GameplayStateManager.GameplayState.REAL_WORLD:
            GameplayStateManager.change_real_world_state(save_data.real_world_state)
    
    return true
```

## 调试和开发工具

### 状态监控器

创建一个开发工具来实时监控状态：

```gdscript
# 状态调试器 (Debug/state_debugger.gd)
extends Control

@onready var app_state_label = $VBox/AppStateLabel
@onready var gameplay_state_label = $VBox/GameplayStateLabel
@onready var real_world_state_label = $VBox/RealWorldStateLabel

func _ready():
    AppStateManager.state_changed.connect(_update_display)
    GameplayStateManager.gameplay_state_changed.connect(_update_display)
    GameplayStateManager.real_world_state_changed.connect(_update_display)
    _update_display()

func _update_display():
    app_state_label.text = "App State: " + AppStateManager.get_current_state_name()
    gameplay_state_label.text = "Gameplay State: " + GameplayStateManager.get_current_state_name()
    real_world_state_label.text = "Real World State: " + GameplayStateManager.get_current_real_world_state_name()

# 可以添加按钮来手动触发状态转换进行测试
func _on_trigger_night_button_pressed():
    GameplayStateManager.pressure_maxed()

func _on_trigger_sleep_button_pressed():
    GameplayStateManager.fall_asleep()
```

### 控制台命令

在开发过程中，可以添加控制台命令快速测试状态转换：

```gdscript
# 在主场景或调试场景中
extends Node

func _input(event):
    if not OS.is_debug_build():
        return
    
    if event is InputEventKey and event.pressed:
        match event.keycode:
            KEY_F1:
                AppStateManager.start_game()
            KEY_F2:
                AppStateManager.pause_game()
            KEY_F3:
                GameplayStateManager.pressure_maxed()
            KEY_F4:
                GameplayStateManager.fall_asleep()
            KEY_F5:
                GameplayStateManager.encounter_enemy()
            # 添加更多快捷键...
```

## 常见问题和解决方案

### Q: 状态转换失败了怎么办？

A: 状态机会输出警告信息到控制台。检查：
1. 状态转换是否符合设计图中的转换规则
2. 是否在正确的状态下调用转换方法
3. GameplayStateManager 是否处于活跃状态（AppStateManager 为 IN_GAME）

### Q: 如何添加新的状态？

A: 按以下步骤：
1. 在相应的枚举中添加新状态
2. 在 `_is_valid_transition()` 方法中添加转换规则
3. 在 `_enter_state()` 和 `_exit_state()` 方法中添加处理逻辑
4. 添加相应的便捷方法

### Q: 状态机与场景管理如何协作？

A: 
- AppStateManager 自动处理程序级场景切换（通过 SceneManager）
- GameplayStateManager 不直接管理场景，而是发送信号让其他系统响应
- 建议在游戏控制器中监听状态变化信号来处理场景内容的切换

### Q: 如何处理状态持久化？

A: 
- 保存当前的 AppState、GameplayState 和 RealWorldState
- 加载时先恢复 AppState，然后根据需要恢复游戏内状态
- 注意状态恢复的顺序，确保状态机处于正确的层级关系

## 性能考虑

1. **状态转换开销**: 状态转换本身很轻量，主要开销在于状态进入/退出时的逻辑处理
2. **信号系统**: 合理使用信号连接，避免过多的监听器
3. **状态查询**: `is_xxx()` 方法开销很小，可以在 `_process()` 中安全使用
4. **避免频繁转换**: 设计游戏逻辑时避免每帧都进行状态转换

## 扩展建议

1. **状态历史**: 可以添加状态历史记录功能，用于调试和回退
2. **条件转换**: 可以添加带条件的状态转换，提高灵活性
3. **状态组**: 对于复杂的状态可以进一步细分为子状态组
4. **异步状态**: 对于需要加载资源的状态，可以添加异步转换支持

通过这个状态机系统，您可以清晰地管理游戏的各种状态，确保状态转换的正确性和代码的可维护性。