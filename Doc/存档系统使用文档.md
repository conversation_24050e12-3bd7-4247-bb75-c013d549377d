# 存档系统使用文档

## 概述

SaveManager 是 wake 项目的全局存档管理系统，作为 autoload 单例运行，提供完整的多存档槽、自动保存和游戏设置管理功能。

## 系统架构

### 核心组件

```
SaveManager (AutoLoad)
├── 存档槽管理 (0-9 槽位)
├── 自动保存系统
├── 数据序列化/反序列化
├── 设置管理系统 (独立)
└── 信号通知系统
```

### 文件结构

```
user://
├── saves/              # 存档目录
│   ├── slot_0.save    # 自动保存专用槽位
│   ├── slot_1.save    # 用户存档槽位 1
│   └── ...           # 最多 10 个槽位 (0-9)
└── settings.cfg       # 游戏设置文件 (INI格式)
```

## 主要功能

### 1. 多存档槽支持

- **最大槽位数**: 10 个 (0-9)
- **槽位 0**: 专用于自动保存
- **槽位 1-9**: 用户手动存档

### 2. 自动保存系统

- **默认间隔**: 120 秒 (2 分钟)
- **自动触发**: 游戏状态改变时
- **可配置**: 启用/禁用、间隔设置

### 3. 游戏设置管理

- **文件格式**: INI/CFG 格式
- **存储位置**: `user://settings.cfg`
- **分组管理**: 支持多个设置分组
- **实时保存**: 设置修改后立即保存

### 4. 存档数据结构

```json
{
  "metadata": {
    "slot": 1,
    "save_time": "2024-01-01 12:00:00",
    "game_version": "1.0.0",
    "playtime": 3600.0,
    "level": 5,
    "location": "res://Scene/Map/level1.tscn"
  },
  "game_state": {
    "app_state": 1,
    "app_state_name": "IN_GAME",
    "gameplay_state": 2,
    "gameplay_state_name": "DREAM_WORLD"
  },
  "player_data": {},
  "world_data": {}
}
```

### 5. 设置文件结构

```ini
[game_settings]
master_volume=1.0
music_volume=0.8
sfx_volume=0.8
fullscreen=false
vsync=true
language="zh_CN"
autosave_enabled=true
autosave_interval=120.0

[controls]
move_up="w"
move_down="s"
move_left="a"
move_right="d"
action="f"
menu="escape"

[display]
resolution_width=1920
resolution_height=1080
window_mode=0
```

## API 参考

### 存档管理 API

#### 基础存档操作

#### 保存游戏
```gdscript
# 保存到指定槽位
SaveManager.save_game(slot: int) -> bool

# 示例
SaveManager.save_game(1)  # 保存到槽位 1
```

#### 加载游戏
```gdscript
# 从指定槽位加载
SaveManager.load_game(slot: int) -> bool

# 示例
SaveManager.load_game(1)  # 从槽位 1 加载
```

#### 检查存档是否存在
```gdscript
SaveManager.has_save(slot: int) -> bool

# 示例
if SaveManager.has_save(1):
    print("槽位 1 存在存档")
```

### 存档信息管理

#### 获取存档信息
```gdscript
# 获取单个槽位信息
SaveManager.get_save_info(slot: int) -> Dictionary

# 获取所有槽位信息
SaveManager.get_all_save_info() -> Array

# 示例
var info = SaveManager.get_save_info(1)
if not info.is_empty():
    print("保存时间: ", info.save_time)
    print("游戏等级: ", info.level)
    print("存档位置: ", info.location)
```

#### 删除存档
```gdscript
SaveManager.delete_save(slot: int) -> bool

# 示例
SaveManager.delete_save(1)  # 删除槽位 1 的存档
```

#### 复制存档
```gdscript
SaveManager.copy_save(from_slot: int, to_slot: int) -> bool

# 示例
SaveManager.copy_save(1, 2)  # 将槽位 1 复制到槽位 2
```

### 自动保存控制

#### 启动/停止自动保存
```gdscript
SaveManager.start_autosave()  # 启动自动保存
SaveManager.stop_autosave()   # 停止自动保存
```

#### 配置自动保存
```gdscript
# 设置自动保存间隔 (秒)
SaveManager.set_autosave_interval(600.0)  # 10 分钟

# 启用/禁用自动保存
SaveManager.set_autosave_enabled(true)   # 启用
SaveManager.set_autosave_enabled(false)  # 禁用
```

#### 手动触发自动保存
```gdscript
SaveManager.autosave() -> bool
```

#### 自动保存相关操作
```gdscript
# 检查是否有自动保存
SaveManager.has_autosave() -> bool

# 从自动保存加载
SaveManager.load_autosave() -> bool
```

### 快捷操作

#### 快速保存/加载
```gdscript
# 快速保存 (使用当前槽位或槽位 1)
SaveManager.quick_save() -> bool

# 快速加载 (加载最新的存档)
SaveManager.quick_load() -> bool
```

### 状态查询

#### 获取当前状态
```gdscript
# 获取当前存档槽位
var current_slot = SaveManager.get_current_save_slot()

# 获取当前存档数据
var save_data = SaveManager.get_save_data()
```

### 设置管理 API

#### 基础设置操作

##### 获取设置值
```gdscript
# 获取设置值 (默认分组)
SaveManager.get_setting(key: String) -> Variant

# 获取指定分组的设置值
SaveManager.get_setting(key: String, section: String) -> Variant

# 示例
var volume = SaveManager.get_setting("master_volume")
var move_key = SaveManager.get_setting("move_up", "controls")
```

##### 设置值
```gdscript
# 设置值 (默认分组)
SaveManager.set_setting(key: String, value: Variant) -> bool

# 设置指定分组的值
SaveManager.set_setting(key: String, value: Variant, section: String) -> bool

# 示例
SaveManager.set_setting("master_volume", 0.8)
SaveManager.set_setting("move_up", "w", "controls")
```

##### 检查设置是否存在
```gdscript
SaveManager.has_setting(key: String, section: String = "game_settings") -> bool

# 示例
if SaveManager.has_setting("master_volume"):
    print("音量设置存在")
```

#### 设置分组管理

##### 获取所有设置
```gdscript
# 获取所有设置
SaveManager.get_all_settings() -> Dictionary

# 获取指定分组的设置
SaveManager.get_settings_section(section: String) -> Dictionary

# 示例
var all_settings = SaveManager.get_all_settings()
var controls = SaveManager.get_settings_section("controls")
```

##### 重置设置
```gdscript
# 重置所有设置到默认值
SaveManager.reset_settings() -> bool

# 重置指定分组的设置
SaveManager.reset_settings_section(section: String) -> bool

# 示例
SaveManager.reset_settings()  # 重置所有设置
SaveManager.reset_settings_section("controls")  # 重置控制设置
```

#### 便捷设置方法

##### 音频设置
```gdscript
# 主音量
SaveManager.get_master_volume() -> float
SaveManager.set_master_volume(volume: float) -> bool

# 音乐音量
SaveManager.get_music_volume() -> float
SaveManager.set_music_volume(volume: float) -> bool

# 音效音量
SaveManager.get_sfx_volume() -> float
SaveManager.set_sfx_volume(volume: float) -> bool

# 示例
SaveManager.set_master_volume(0.8)
var music_vol = SaveManager.get_music_volume()
```

##### 显示设置
```gdscript
# 全屏设置
SaveManager.is_fullscreen() -> bool
SaveManager.set_fullscreen(enabled: bool) -> bool

# 垂直同步
SaveManager.is_vsync_enabled() -> bool
SaveManager.set_vsync_enabled(enabled: bool) -> bool

# 语言设置
SaveManager.get_language() -> String
SaveManager.set_language(lang: String) -> bool

# 示例
SaveManager.set_fullscreen(true)
SaveManager.set_language("en_US")
```

#### 自动保存设置同步

```gdscript
# 从设置文件同步自动保存配置
SaveManager.sync_autosave_settings()

# 保存当前自动保存配置到设置文件
SaveManager.save_autosave_settings()
```

## 信号系统

### 可监听的信号

```gdscript
# 存档完成信号
SaveManager.save_completed.connect(_on_save_completed)
func _on_save_completed(slot: int, success: bool):
    if success:
        print("槽位 %d 保存成功" % slot)
    else:
        print("槽位 %d 保存失败" % slot)

# 加载完成信号
SaveManager.load_completed.connect(_on_load_completed)
func _on_load_completed(slot: int, success: bool):
    if success:
        print("槽位 %d 加载成功" % slot)

# 自动保存完成信号
SaveManager.autosave_completed.connect(_on_autosave_completed)
func _on_autosave_completed(success: bool):
    if success:
        print("自动保存完成")

# 设置改变信号
SaveManager.settings_changed.connect(_on_settings_changed)
func _on_settings_changed(key: String, value: Variant):
    print("设置已更改: %s = %s" % [key, str(value)])
    # 可以在这里应用设置到游戏中
    match key:
        "master_volume":
            AudioServer.set_bus_volume_db(0, linear_to_db(value))
        "fullscreen":
            if value:
                DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
            else:
                DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_WINDOWED)
```

## 使用示例

### 1. 基础存档界面实现

```gdscript
extends Control

@onready var save_slots_container = $SaveSlotsContainer

func _ready():
    _update_save_slots_ui()
    
    # 监听存档信号
    SaveManager.save_completed.connect(_on_save_completed)
    SaveManager.load_completed.connect(_on_load_completed)

func _update_save_slots_ui():
    var save_infos = SaveManager.get_all_save_info()
    
    for i in range(1, SaveManager.MAX_SAVE_SLOTS):  # 跳过自动保存槽位 0
        var slot_button = save_slots_container.get_child(i - 1)
        var info = save_infos[i]
        
        if info == null:
            slot_button.text = "空槽位 %d" % i
        else:
            slot_button.text = "槽位 %d\n%s\n等级 %d" % [i, info.save_time, info.level]

func _on_save_button_pressed(slot: int):
    SaveManager.save_game(slot)

func _on_load_button_pressed(slot: int):
    if SaveManager.has_save(slot):
        SaveManager.load_game(slot)

func _on_save_completed(slot: int, success: bool):
    if success:
        _update_save_slots_ui()
        _show_message("游戏已保存到槽位 %d" % slot)
    else:
        _show_message("保存失败")

func _on_load_completed(slot: int, success: bool):
    if success:
        _show_message("游戏已从槽位 %d 加载" % slot)
    else:
        _show_message("加载失败")
```

### 2. 游戏内快捷键实现

```gdscript
extends Node

func _input(event):
    if event.is_action_pressed("quick_save"):  # F5
        SaveManager.quick_save()
        
    elif event.is_action_pressed("quick_load"):  # F9
        SaveManager.quick_load()
        
    elif event.is_action_pressed("autosave_toggle"):  # F12
        var enabled = not SaveManager.autosave_enabled
        SaveManager.set_autosave_enabled(enabled)
        print("自动保存已%s" % ("启用" if enabled else "禁用"))
```

### 3. 设置界面实现

```gdscript
extends Control

@onready var master_volume_slider = $VBoxContainer/MasterVolumeSlider
@onready var music_volume_slider = $VBoxContainer/MusicVolumeSlider
@onready var fullscreen_checkbox = $VBoxContainer/FullscreenCheckBox
@onready var language_option = $VBoxContainer/LanguageOption

func _ready():
    _load_settings_to_ui()
    
    # 监听设置改变
    SaveManager.settings_changed.connect(_on_settings_changed)
    
    # 连接UI信号
    master_volume_slider.value_changed.connect(_on_master_volume_changed)
    music_volume_slider.value_changed.connect(_on_music_volume_changed)
    fullscreen_checkbox.toggled.connect(_on_fullscreen_toggled)
    language_option.item_selected.connect(_on_language_selected)

func _load_settings_to_ui():
    # 从SaveManager加载设置到UI
    master_volume_slider.value = SaveManager.get_master_volume()
    music_volume_slider.value = SaveManager.get_music_volume()
    fullscreen_checkbox.button_pressed = SaveManager.is_fullscreen()
    
    # 设置语言选项
    var current_lang = SaveManager.get_language()
    match current_lang:
        "zh_CN":
            language_option.selected = 0
        "en_US":
            language_option.selected = 1

func _on_master_volume_changed(value: float):
    SaveManager.set_master_volume(value)

func _on_music_volume_changed(value: float):
    SaveManager.set_music_volume(value)

func _on_fullscreen_toggled(pressed: bool):
    SaveManager.set_fullscreen(pressed)

func _on_language_selected(index: int):
    var languages = ["zh_CN", "en_US"]
    if index < languages.size():
        SaveManager.set_language(languages[index])

func _on_settings_changed(key: String, value: Variant):
    # 应用设置到游戏
    match key:
        "master_volume":
            AudioServer.set_bus_volume_db(0, linear_to_db(value))
        "music_volume":
            AudioServer.set_bus_volume_db(1, linear_to_db(value))
        "fullscreen":
            if value:
                DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
            else:
                DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_WINDOWED)
        "language":
            _apply_language(value)

func _apply_language(lang: String):
    # 应用语言设置
    match lang:
        "zh_CN":
            TranslationServer.set_locale("zh_CN")
        "en_US":
            TranslationServer.set_locale("en_US")

func _on_reset_button_pressed():
    # 重置所有设置
    SaveManager.reset_settings()
    _load_settings_to_ui()

func _on_reset_controls_button_pressed():
    # 只重置控制设置
    SaveManager.reset_settings_section("controls")
```

### 4. 控制设置界面

```gdscript
extends Control

@onready var key_bindings_container = $ScrollContainer/VBoxContainer

var action_names = ["move_up", "move_down", "move_left", "move_right", "action", "menu"]
var action_labels = ["上移", "下移", "左移", "右移", "动作", "菜单"]

func _ready():
    _setup_key_binding_ui()
    SaveManager.settings_changed.connect(_on_settings_changed)

func _setup_key_binding_ui():
    # 创建按键绑定UI
    for i in range(action_names.size()):
        var action = action_names[i]
        var label_text = action_labels[i]
        
        var hbox = HBoxContainer.new()
        var label = Label.new()
        var button = Button.new()
        
        label.text = label_text + ":"
        label.custom_minimum_size.x = 100
        
        var current_key = SaveManager.get_setting(action, "controls")
        button.text = current_key if current_key else "未设置"
        button.pressed.connect(_on_key_button_pressed.bind(action, button))
        
        hbox.add_child(label)
        hbox.add_child(button)
        key_bindings_container.add_child(hbox)

func _on_key_button_pressed(action: String, button: Button):
    button.text = "按下任意键..."
    button.disabled = true
    
    # 等待按键输入
    var input_event = await _wait_for_key_input()
    
    if input_event is InputEventKey:
        var key_name = input_event.as_text_physical_keycode()
        SaveManager.set_setting(action, key_name, "controls")
        button.text = key_name
    
    button.disabled = false

func _wait_for_key_input() -> InputEvent:
    while true:
        await get_tree().process_frame
        var events = Input.get_vector("", "", "", "")
        # 这里简化处理，实际实现需要监听具体按键事件
        if Input.is_anything_pressed():
            return InputEventKey.new()

func _on_settings_changed(key: String, value: Variant):
    if key in action_names:
        # 更新按键绑定
        _update_input_map(key, value)

func _update_input_map(action: String, key: String):
    # 更新InputMap
    if InputMap.has_action(action):
        InputMap.action_erase_events(action)
        
        var event = InputEventKey.new()
        event.physical_keycode = OS.find_keycode_from_string(key)
        InputMap.action_add_event(action, event)
```

### 5. 自动保存状态提示

```gdscript
extends Control

@onready var autosave_indicator = $AutosaveIndicator

func _ready():
    SaveManager.autosave_completed.connect(_on_autosave_completed)

func _on_autosave_completed(success: bool):
    if success:
        _show_autosave_indicator()

func _show_autosave_indicator():
    autosave_indicator.modulate.a = 1.0
    var tween = create_tween()
    tween.tween_delay(2.0)
    tween.tween_property(autosave_indicator, "modulate:a", 0.0, 1.0)
```

## 最佳实践

### 1. 存档与设置分离

- **存档**: 保存游戏进度、玩家数据、世界状态
- **设置**: 保存用户偏好、控制绑定、显示选项
- **独立管理**: 两者使用不同的文件格式和API

### 2. 设置应用时机

```gdscript
# 游戏启动时应用设置
func _ready():
    _apply_all_settings()

func _apply_all_settings():
    # 应用音频设置
    AudioServer.set_bus_volume_db(0, linear_to_db(SaveManager.get_master_volume()))
    AudioServer.set_bus_volume_db(1, linear_to_db(SaveManager.get_music_volume()))
    
    # 应用显示设置
    if SaveManager.is_fullscreen():
        DisplayServer.window_set_mode(DisplayServer.WINDOW_MODE_FULLSCREEN)
    
    # 应用语言设置
    TranslationServer.set_locale(SaveManager.get_language())
```

### 3. 存档时机建议

- **关键进度点**: 完成任务、进入新区域
- **状态切换时**: 场景切换、重要事件后
- **玩家主动**: 通过菜单或快捷键

### 2. 自动保存策略

```gdscript
# 在关键游戏事件后启动自动保存
func _on_level_completed():
    SaveManager.start_autosave()

# 在战斗开始前停止自动保存
func _on_battle_started():
    SaveManager.stop_autosave()

# 战斗结束后恢复自动保存
func _on_battle_ended():
    SaveManager.start_autosave()
```

### 3. 错误处理

```gdscript
func safe_save_game(slot: int):
    if slot < 0 or slot >= SaveManager.MAX_SAVE_SLOTS:
        _show_error("无效的存档槽位")
        return
    
    if not SaveManager.save_game(slot):
        _show_error("保存失败，请检查磁盘空间")

func safe_load_game(slot: int):
    if not SaveManager.has_save(slot):
        _show_error("该槽位没有存档")
        return
    
    if not SaveManager.load_game(slot):
        _show_error("加载失败，存档文件可能已损坏")
```

## 扩展说明

### 存档数据自定义

如需保存额外的游戏数据，可以修改 `SaveManager._collect_save_data()` 方法：

```gdscript
func _collect_save_data() -> Dictionary:
    var data = {
        # ... 现有数据结构
    }
    
    # 添加自定义数据
    if PlayerManager:
        data.player_data = PlayerManager.get_save_data()
    
    if InventoryManager:
        data.world_data["inventory"] = InventoryManager.get_save_data()
    
    return data
```

### 存档数据恢复自定义

相应地在 `_apply_save_data()` 方法中添加数据恢复逻辑：

```gdscript
func _apply_save_data(data: Dictionary):
    # ... 现有恢复逻辑
    
    # 恢复自定义数据
    if data.has("player_data") and PlayerManager:
        PlayerManager.load_save_data(data.player_data)
    
    if data.has("world_data") and data.world_data.has("inventory") and InventoryManager:
        InventoryManager.load_save_data(data.world_data.inventory)
```

### 设置扩展

如需添加新的设置类型，可以在 `_create_default_settings()` 中添加：

```gdscript
# 添加新的设置分组
settings_config.set_value("graphics", "texture_quality", "high")
settings_config.set_value("graphics", "shadow_quality", "medium")
settings_config.set_value("graphics", "anti_aliasing", true)

# 添加新的游戏设置
settings_config.set_value(DEFAULT_SETTINGS_SECTION, "difficulty", "normal")
settings_config.set_value(DEFAULT_SETTINGS_SECTION, "auto_pause", true)
```

## 注意事项

1. **文件权限**: 确保游戏对 `user://` 目录有写入权限
2. **存档兼容性**: 游戏版本更新时需考虑存档数据兼容性
3. **设置兼容性**: 新增设置项时要提供默认值
4. **磁盘空间**: 大量存档可能占用较多磁盘空间
5. **性能考虑**: 存档操作在主线程执行，大数据量时可能有短暂卡顿
6. **数据安全**: 存档文件以明文 JSON 格式保存，设置文件以明文 INI 格式保存
7. **设置文件位置**: 设置文件存储在用户目录，便于在不同游戏版本间保持
8. **分组管理**: 使用分组来组织不同类型的设置，便于管理和重置

## 技术细节

### 存档系统
- **文件格式**: JSON (.save)
- **存储位置**: `user://saves/`
- **编码**: UTF-8
- **最大槽位**: 10 个 (可在常量中修改)
- **自动保存槽位**: 固定为槽位 0
- **默认自动保存间隔**: 120 秒

### 设置系统
- **文件格式**: INI/CFG (.cfg)
- **存储位置**: `user://settings.cfg`
- **编码**: UTF-8
- **分组支持**: 支持多个设置分组
- **实时保存**: 设置修改后立即保存到文件
- **默认分组**: `game_settings`
- **内建分组**: `game_settings`, `controls`, `display`