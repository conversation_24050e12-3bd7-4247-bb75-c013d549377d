stateDiagram-v2
%% =======================================================
%% 1. Top-Level Application FSM  (autoload singletons)
%% =======================================================
[*] --> MainMenu
MainMenu --> InGame        : StartGame / Continue
MainMenu --> Settings      : OpenSettings
MainMenu --> QuitApp       : ExitProgram
Settings --> MainMenu      : Back
Settings --> Paused        : Back
InGame   --> Paused        : PressPause
Paused   --> InGame        : Resume
Paused   --> Settings      : OpenSettings
Paused   --> MainMenu      : QuitToMenu / SaveAndExit
Paused   --> QuitApp       : ExitProgram
%% 任一子 FSM 触发 -> GameOver，再由 GameOver -> MainMenu / InGame
InGame   --> GameOver      : PlayerDefeated / CriticalFail
GameOver --> MainMenu      : BackToMenu
GameOver --> InGame        : Retry / LoadSave

%% =======================================================
%% 2. Nested Gameplay FSM (runs only when AppFSM == InGame)
%% =======================================================
state InGame {
    direction LR
    [*] --> RealWorld : LevelLoaded
    
    %% -- Real-World Micro-Cycle (Day/Night/Boss) --
    state RealWorld {
        direction LR
        Day    --> Night   : PressureMaxed
        Night  --> BossDay : EnterBossGate
        BossDay --> Day    : BossDefeated
    }

    %% -- Dream-World Macro-Cycle --
    DreamWorld : InsideWorld
    Battle     : NormalBattle
    BossBattle : BossBattle
    ChapterEnd : ChapterEnd
    RealWorld --> DreamWorld : FallAsleep
    DreamWorld --> Battle    : EncounterEnemy
    Battle --> DreamWorld    : WinBattle
    Battle --> BossBattle    : TriggerBoss
    BossBattle --> DreamWorld: BossDefeated
    DreamWorld --> ChapterEnd: ObjectivesComplete
    ChapterEnd --> RealWorld : WakeUp
    
    %% -- Fail / Success hooks raising to AppFSM --
    DreamWorld --> GameOver  : PlayerDefeated
    BossBattle --> GameOver  : PlayerDefeated
}
