# 全局管理器 Autoload 设置指南

## 重构后的管理器架构

经过重构，全局管理器现在遵循单一职责原则，各司其职：

### 管理器职责分工

1. **SaveManager** - 存档和设置管理
2. **SettingsApplier** - 设置应用到系统
3. **InputHandler** - 全局输入处理
4. **SceneManager** - 场景和UI管理
5. **AppStateManager** - 应用状态管理
6. **GameplayStateManager** - 游戏内状态管理
7. **GameManager** - 管理器协调

### 推荐的 Autoload 加载顺序

在 Godot 项目设置中，按以下顺序添加 autoload：

```
1. SaveManager          - res://Script/GlobalManager/save_manager.gd
2. SettingsApplier       - res://Script/GlobalManager/settings_applier.gd
3. InputHandler          - res://Script/GlobalManager/input_handler.gd
4. SceneManager          - res://Script/GlobalManager/scene_manager.gd
5. AppStateManager       - res://Script/GlobalManager/app_state_manager.gd
6. GameplayStateManager  - res://Script/GlobalManager/gameplay_state_manager.gd
7. GameManager           - res://Script/GlobalManager/game_manager.gd
```

### 依赖关系说明

```
SaveManager (基础层)
    ↓
SettingsApplier (依赖 SaveManager)
    ↓
InputHandler (独立)
    ↓
SceneManager (依赖 InputHandler)
    ↓
AppStateManager (依赖 SceneManager)
    ↓
GameplayStateManager (依赖 AppStateManager)
    ↓
GameManager (协调层，依赖所有其他管理器)
```

### 调用方向规则

1. **向下调用**: 上层管理器可以调用下层管理器
2. **信号通信**: 下层管理器通过信号通知上层管理器
3. **避免循环**: 严格禁止循环依赖

### 重构收益

- ✅ 消除了循环依赖
- ✅ 每个管理器职责单一明确
- ✅ 代码更易维护和测试
- ✅ 暂停菜单管理统一到 SceneManager
- ✅ 设置应用逻辑独立到 SettingsApplier
- ✅ 输入处理独立到 InputHandler
- ✅ GameManager 简化为纯协调功能

### 迁移注意事项

1. **测试脚本**: SaveTestManager 已被注释，不会在正式版本运行
2. **向后兼容**: GameManager 保留了向后兼容接口
3. **信号连接**: 确保新的信号连接正确建立
4. **调试代码**: 标记了可在发布版本中注释的调试方法

### 验证步骤

重构完成后，请验证：

1. 游戏启动正常
2. 暂停菜单功能正常
3. 设置保存和应用正常
4. 场景切换正常
5. 状态管理正常
6. 无循环依赖错误
