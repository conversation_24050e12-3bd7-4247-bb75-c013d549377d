# 存档系统测试脚本使用说明

## 概述

`save_test_manager.gd` 是一个专门用于测试 SaveManager 和 GameManager 功能的自动化测试脚本。该脚本通过定时器和直接函数调用完成全面的功能测试。

## 使用方法

### 1. 临时添加到项目

在 `project.godot` 的 `[autoload]` 部分临时添加：

```ini
[autoload]
# ... 其他autoload ...
SaveTestManager="*res://Script/GlobalManager/save_test_manager.gd"  # 临时测试用
```

### 2. 运行测试

启动游戏后，测试会自动在进入主菜单时开始：
- **等待状态**: 测试脚本会等待AppState进入MAIN_MENU状态
- **总测试时间**: 约45秒（从主菜单开始计算，包含自动保存等待时间）
- **测试间隔**: 每2秒执行一组测试
- **自动输出**: 测试结果自动输出到控制台

### 3. 移除测试脚本

测试完成后：
1. 从 `project.godot` 中移除 `SaveTestManager` 行
2. 脚本会自动清理并移除自己

## 测试流程

### 🔄 完整测试周期

1. **状态等待** - 等待AppState进入MAIN_MENU状态
2. **环境备份** - 保存原始设置和状态
3. **环境清理** - 清除可能存在的测试数据
4. **功能测试** - 执行各项测试用例
5. **环境恢复** - 完全恢复到测试前状态
6. **脚本移除** - 自动清理测试脚本

### 📋 环境管理

#### 🛡️ 测试前保护
- 备份所有原始设置到内存
- 备份InputMap原始状态
- 备份自动保存配置
- 清理所有可能存在的测试数据

#### 🧹 测试后清理
- 删除所有测试创建的存档
- 移除所有测试设置键
- 恢复原始设置值
- 恢复InputMap到原始状态
- 恢复自动保存配置

### ⚙️ 测试数据隔离

所有测试数据使用特定前缀，便于识别和清理：
- 测试设置键: `test_*`, `perf_test_*`, `*_test`
- 测试设置分组: `test_section`, `temp_test`, `performance_test`
- 测试存档槽: 1, 2, 3（完全清理）

### 🔧 基础功能测试
- [x] 管理器可用性检查
- [x] 初始状态验证
- [x] 存档保存/加载操作
- [x] 设置读写操作
- [x] 控制键位设置

### 🎮 高级功能测试
- [x] 自动保存功能（包含5秒间隔真实测试）
- [x] 快速保存/加载
- [x] 存档复制功能
- [x] 设置重置功能
- [x] InputMap自动更新

### ⚠️ 边界情况测试
- [x] 无效参数处理
- [x] 数值边界检查
- [x] 空值处理
- [x] 错误恢复机制

### 📊 性能测试
- [x] 存档操作性能
- [x] 设置操作性能
- [x] 压力测试

## 手动测试控制

运行时可以手动触发额外测试：

- **空格键**: 触发压力测试（快速连续操作）
- **回车键**: 触发性能测试（测量操作耗时）

## 测试输出示例

```
[SaveTest] === 存档和配置系统测试开始 ===
[SaveTest] 开始自动化测试...
[SaveTest] --- 执行立即测试 ---
[SaveTest] 测试管理器可用性...
[SaveTest] ✓ SaveManager应该可用
[SaveTest] ✓ SaveManager应该有save_game方法
[SaveTest] ✓ 管理器可用性测试完成
[SaveTest] 测试存档操作...
[SaveTest] ✓ 存档到槽位 1 应该成功
[SaveTest] ✓ 槽位 1 应该存在存档
...
[SaveTest] === 测试完成 ===
[SaveTest] === 测试总结 ===
[SaveTest] 总测试数: 45
[SaveTest] 通过: 45
[SaveTest] 失败: 0
[SaveTest] 成功率: 100.0%
[SaveTest] 🎉 所有测试通过！
```

## 测试配置

可以修改脚本顶部的常量来调整测试：

```gdscript
const TEST_DURATION = 45.0  # 总测试时间（包含自动保存等待）
const TEST_INTERVAL = 2.0   # 测试间隔
```

## 测试数据

脚本使用预定义的测试数据：

```gdscript
var test_save_slots = [1, 2, 3]  # 测试存档槽
var test_settings = {
    "master_volume": [0.5, 0.8, 1.0],
    "fullscreen": [true, false, true],
    # ...
}
var test_controls = {
    "move_up": ["w", "i", "w"],
    # ...
}
```

## 注意事项

### ⚠️ 重要提醒

1. **仅测试环境使用**: 此脚本仅用于开发测试，不应在发布版本中包含
2. **数据清理**: 测试会创建临时存档和设置，但会在结束时自动清理
3. **性能影响**: 测试期间会频繁进行文件操作，可能影响游戏性能
4. **依赖关系**: 确保 SaveManager 和 GameManager 已正确配置

### 🔧 故障排除

**测试失败时的检查项目**:

1. 确认 SaveManager 和 GameManager 已正确加载
2. 检查文件权限，确保可以写入 `user://` 目录
3. 验证 InputMap 中存在测试的按键动作
4. 查看控制台输出中的具体错误信息

### 📝 自定义测试

可以添加自定义测试：

```gdscript
# 在游戏代码中调用
SaveTestManager.run_custom_test("我的测试", func():
    # 自定义测试逻辑
    SaveTestManager._assert(条件, "测试描述")
)
```

## 测试时间线

```
启动 - 等待AppState进入MAIN_MENU状态
0s  - 测试开始，检查管理器可用性
2s  - 存档操作测试
4s  - 设置操作测试
6s  - 控制设置测试
8s  - 加载操作测试
10s - 高级功能测试（包含自动保存5秒等待）
18s - 边界情况测试
20s - 清理和总结
22s - 测试完成，显示结果
25s - 自动移除脚本
```

这个测试脚本提供了全面的自动化测试，帮助确保存档和配置系统的稳定性和正确性。