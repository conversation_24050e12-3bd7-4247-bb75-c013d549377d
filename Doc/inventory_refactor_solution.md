# 物品系统重构解决方案

## 问题分析

原系统存在的核心问题：
1. **概念混淆**：`tags` 被用来存储物品的固有属性，但在使用过程中被当作可变状态处理
2. **数据污染**：物品模板的 `use` 标签被直接修改，破坏了模板的完整性
3. **职责不清**：背包槽位既管理位置又管理物品状态，违反单一职责原则

## 解决方案架构

### 1. 三层分离架构

```
ItemTemplate (物品模板)
├── 固有属性：id, name, description, icon_path
├── 不可变标签：tags (攻击力、槽位、基础使用次数等)
└── 类型信息：type, stack_size, value, rarity

ItemInstance (物品实例)
├── 模板引用：template
├── 可变状态：instance_data
│   ├── remaining_uses (剩余使用次数)
│   ├── created_time (创建时间)
│   └── 其他实例特有数据...
└── 状态方法：is_usable(), consume_use(), get_remaining_uses()

InventorySlot (背包槽位)
├── 实例数组：item_instances[]
├── 模板ID：template_id
└── 管理方法：add_instances(), remove_instances(), can_stack_with()
```

### 2. 核心类设计

#### ItemTemplate
- **职责**：存储物品的不可变定义
- **特点**：一旦创建，属性不可修改
- **用途**：作为所有同类物品实例的共享模板

#### ItemInstance
- **职责**：存储单个物品的可变状态
- **特点**：每个实例都有独立的状态
- **用途**：处理使用次数、耐久度等个性化属性

#### InventorySlot
- **职责**：管理背包中的位置和数量
- **特点**：只关心存储逻辑，不关心物品属性
- **用途**：实现堆叠、移动等背包操作

### 3. TagUtil 适配

```gdscript
# 支持两种类型的标签访问
static func tagi(item, key: String, default_value: int = 0) -> int:
    var tags = _get_item_tags(item)
    return tags.get(key, default_value)

# 智能获取标签
static func _get_item_tags(item) -> Dictionary:
    # ItemInstance -> 获取模板的tags
    if item.has("template"):
        return item.template.tags
    # ItemTemplate -> 直接获取tags
    elif item.has("tags"):
        return item.tags
    return {}
```

### 4. 使用次数管理

```gdscript
# 错误的做法（修改模板）
template.tags["use"] = template.tags["use"] - 1  # ❌

# 正确的做法（修改实例）
instance.consume_use()  # ✅
```

## 实现细节

### 1. 向后兼容

保留原有的 `Item` 和 `Equipment` 类作为 `ItemTemplate` 和 `EquipmentTemplate` 的别名：

```gdscript
class Item extends ItemTemplate:
    func _init(item_id: String = "", item_name: String = "", item_desc: String = ""):
        super(item_id, item_name, item_desc)

class Equipment extends EquipmentTemplate:
    func _init(item_id: String = "", item_name: String = "", item_desc: String = ""):
        super(item_id, item_name, item_desc)
```

### 2. 数据库适配

```gdscript
# 获取模板
static func get_item(item_id: String) -> InventoryManager.ItemTemplate

# 创建实例
static func create_item_instance(item_id: String) -> InventoryManager.ItemInstance
```

### 3. 背包操作重构

```gdscript
# 添加物品：创建实例并分配到槽位
func add_item(item_id: String, quantity: int = 1) -> bool:
    var template = ItemDatabase.get_item(item_id)
    var instances = []
    for i in range(quantity):
        instances.append(ItemDatabase.create_item_instance(item_id))
    # 分配到槽位...

# 使用物品：操作实例状态
func use_item(slot_index: int) -> bool:
    var instance = slot.item_instances[0]
    TagUtil.consume_use(instance)  # 只修改实例状态
```

## 优势总结

### 1. 数据一致性
- ✅ 物品模板永远不被修改，保证所有同类物品的基础属性一致
- ✅ 实例状态独立，不会相互影响

### 2. 职责清晰
- ✅ ItemTemplate：负责固有属性定义
- ✅ ItemInstance：负责可变状态管理
- ✅ InventorySlot：负责存储和位置管理

### 3. 扩展性强
- ✅ 可以轻松添加新的实例属性（耐久度、强化等级等）
- ✅ 支持复杂的物品状态逻辑
- ✅ 便于实现物品个性化

### 4. 性能优化
- ✅ 模板共享，减少内存占用
- ✅ 实例数据最小化，只存储必要的可变信息

## 迁移指南

### 1. 代码更新
```gdscript
# 旧代码
var item = InventoryManager.Item.new()
TagUtil.consume_use(item)  # 直接修改模板

# 新代码
var template = ItemDatabase.get_item("item_id")
var instance = ItemDatabase.create_item_instance("item_id")
TagUtil.consume_use(instance)  # 修改实例状态
```

### 2. 数据兼容
- JSON数据格式保持不变
- 现有存档自动转换为新格式
- TagUtil函数签名保持兼容

### 3. 测试验证
运行 `Script/Test/inventory_refactor_demo.gd` 查看重构效果。

## 结论

这次重构彻底解决了物品系统中标签概念混淆的问题，建立了清晰的架构分层：
- **模板层**：不可变的物品定义
- **实例层**：可变的状态管理  
- **存储层**：位置和数量管理

新架构不仅解决了当前问题，还为未来的功能扩展奠定了坚实基础。
