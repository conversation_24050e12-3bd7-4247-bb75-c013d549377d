# Inventory & Tag 系统重构任务总览  
*(截至 2025-07-25 与 ChatGPT 的全部讨论纪要)*  

---

## 1. 重构目标  

| # | 目标 | 关键点 |
|---|------|--------|
| **G-1** | **数据驱动** | 彻底去掉硬编码物品定义；外部 `items.json`→运行时加载 / 热更新 |
| **G-2** | **薄管理器** | `InventoryManager` 只做「格子增删换」；`TagUtil` 只做「字典工具」 |
| **G-3** | **业务解耦** | 真正 *加血／加攻／任务触发* 等逻辑落在 *领域服务层*，Manager 不碰 |
| **G-4** | **可扩展 Tag** | 用 `"k:v"` 列表组合行为；保留 `Item.type` 作为宏分类，避免 Tag 混乱 |
| **G-5** | **清晰分层** | UI ↔︎ Manager ↔︎ Service ↔︎ Data，各层单向依赖，信号隔离 |

---

## 2. 新目录/模块规划  

Script
├─ GlobalManager/           # 原有加载顺序保持
│  └─ game_manager.gd       # 仅负责实例化 Service
├─ GameService/             # ★新增：业务逻辑层
│  ├─ item_effect_service.gd
│  ├─ stat_service.gd
│  └─ buff_service.gd       # (可选)
├─ Middleware/
│  ├─ inventory_manager.gd  # 超薄 CRUD + 信号
│  └─ tag_util.gd           # Tag 编解码/工具/注册表
└─ data/
└─ items.json            # 物品原型 & tag 定义

> **说明**  
> - `Middleware/` 可与 `GlobalManager/` 并列或直接放回原处；命名自由。  
> - Service 层 *不* Autoload，由 `game_manager.gd` 手动 `add_child()`。  

---

## 3. 关键脚本职责  

| 模块 | 职责 | **禁止** |
|------|------|----------|
| **InventoryManager** | *槽位* 增删查换、装备装卸、信号；绝不解释 tag | 调用 Player/Quest/Stat 逻辑 |
| **TagUtil** | Tag 字符串 ↔︎ `Dictionary`、通用 `tagi/tagf`、remaining_uses/slot 映射、handler 注册 | 直接操作 Stat/UI |
| **ItemEffectService** | 订 `item_used / equipment_changed` 信号，根据 tag 派发效果 | 存储格子 | 
| **StatService** | 保存角色属性字典、提供 `set_stat()`、广播 `stats_changed` | 读取背包 |
| **UI 层** | 只监听 `stats_changed` / Manager 的信号刷新界面 | 调整 Tag |

> **Tag 处理流**  
> `InventoryManager.use_item()` → **emit** `item_used(item)`  
> `ItemEffectService` 收到 → `TagUtil.apply_use_effects()` → 调 `StatService` → UI 更新  

---

## 4. Tag 设计约定  

| 范畴 | Tag Key | 例值 | 说明 |
|------|---------|------|------|
| 使用 | `use` | `1/-1/0` | >0 一次性；-1 无限；0 不可用 |
| 恢复 | `hp`, `mp`, … | `50` | 正数加、负数扣 |
| 装备 | `slot` | `weapon/armor/...` | 字符串映射到 `EquipmentSlot` |
| 数值 | `atk`, `def`, `spd`, … | `+/-N` | 任意可枚举属性 |
| 其他 | `rarity`, `value`, `quest_id`, `decay_sec`… | 任意 | 业务层决定解释与否 |

**保留顶层字段**：`id / name / desc / icon_path / stack / type` —— 其余全部进 `tags`。  

---

## 5. ItemDatabase & JSON  

```json5
[
  { "id":"potion_health", "name":"生命药水", "icon_path":"res://...", "stack":10,
    "type":0, "tags":"use:1,hp:50,value:50,rarity:0" },

  { "id":"sword_leaf",   "name":"落叶短剑", "stack":1,
    "type":1, "tags":"slot:weapon,atk:12,rarity:1,value:120" }
]

加载流程：

var it := Item.new()
it.tags = TagUtil.parse(def["tags"])
ItemDatabase._db[it.id] = it


⸻

6. 服务层示例（伪代码）

# item_effect_service.gd
func _ready():
    TagUtil.register_handler("hp", func(stat,item,val):
        var new_hp = min(stat.data.hp + val, stat.data.max_hp)
        stat.set_stat("hp", new_hp))
    InventoryManager.item_used.connect(_on_item_used)

func _on_item_used(item):
    if TagUtil.is_usable(item):
        TagUtil.apply_use_effects(stat_service, item)
        TagUtil.consume_use(item)


⸻

7. 重构流程指导
	1.	落脚点定位
	•	复制现有 inventory_manager.gd → Middleware，删硬编码 & 数据函数
	•	新建 tag_util.gd（已示例）
	2.	数据迁移
	•	写转换脚本：旧 JSON / GDScript → tags 字符串
	3.	信号接线
	•	Manager emit_signal("item_used", item) / equipment_changed
	•	Service 脚本接收并打印效果 (print("Heal", val)) 先验证
	4.	插入 StatService
	•	用 print 替代 UI，确认属性变化
	5.	逐步删除旧字段
	•	保留兼容 Getter (get_value()) 过渡期使用
	6.	测试用例
	•	加载存档 → 使用/装备 → 存档 → 读档；确保 tag/stack/uses 正确
	7.	Code Clean-up
	•	Manager 不再依赖 GameManager；TagUtil 不引用任何全局节点
	8.	热更新验证
	•	运行时 ItemDatabase.load(new_path) → 使用新道具打印效果

⸻

8. 后续思考列表
	•	资源化 Item：将 Item 改 Resource，Inspector 直接编辑
	•	Buff/状态机：把持续效果写进 BuffService；装备/消耗统一管理
	•	网络同步：Service 层 RPC，即可在多人模式共享背包/属性
	•	DLC & Mod 支持：Designer 将新 JSON 打包进 DLC 目录，运行时加载即可
	•	安全校验：TagUtil 可加白名单，阻止非法 tag 被解析执行

⸻

9. 临时验证脚本片段

var item := ItemDatabase.get("potion_health")
InventoryManager.add_item(item.id, 1)
InventoryManager.use_item(0) # 控制台应 print: "Heal 50"


⸻

结论：
通过“薄 Manager + Tag 中间件 + 领域服务层”三明治结构，
可以确保 数据灵活、逻辑清晰、业务可插拔，且任何层次膨胀时都能独立演进。

本纪要可直接收录到 Doc/inventory_refactor.md 供团队后续查阅。


