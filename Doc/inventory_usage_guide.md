# 背包系统重构使用指南 (最新版)

## 概述

按照 `inventory_refactor.md` 的设计，背包系统已成功重构为薄管理器 + 标签中间件 + 领域服务层的三层架构。**最新版**进一步优化了单例设计，将工具类从 autoload 中移除，提升了系统的清晰度和性能。

## 新架构组件

### 1. 数据层
- **`data/items.json`** - 物品定义数据文件
- **`ItemDatabase`** - 静态物品数据库工具类（已移除 autoload）

### 2. 中间件层
- **`TagUtil`** - 静态标签解析和效果处理工具类（已移除 autoload）
- **`InventoryManager`** - 薄CRUD操作管理器（仍为 autoload）

### 3. 服务层
- **`StatService`** - 玩家属性管理服务
- **`ItemEffectService`** - 物品效果处理服务

## 最新架构改进

### 🔧 单例设计优化
- **改进**: `ItemDatabase` 和 `TagUtil` 改为纯静态工具类
- **优势**: 减少不必要的 Node 实例，避免单例获取失败问题
- **影响**: API 调用方式保持不变，但不再需要 autoload 注册

### 🔧 服务依赖解析
- **设计**: ItemEffectService 通过 GameManager 获取服务引用
- **实现**: 在 `_setup_service_architecture()` 中正确建立依赖关系

### 🔧 标签格式一致性
- **统一**: 使用 `_get_parsed_tags()` 函数处理所有标签格式
- **缓存**: 解析结果自动缓存，提升性能

## 使用方法

### 添加新物品

1. 在 `data/items.json` 中添加物品定义：
```json
{
  "id": "new_potion",
  "name": "新药水",
  "description": "神奇的新药水",
  "icon_path": "res://Asset/Icon/consumable_new_potion.png",
  "type": 0,
  "stack_size": 10,
  "value": 100,
  "rarity": 2,
  "tags": "use:1,hp:75,mp:25"
}
```

2. 初始化数据库（如需要）：
```gdscript
# 数据库会在首次使用时自动加载，也可手动初始化
ItemDatabase.initialize()

# 或重新加载数据库（开发时）
ItemDatabase.reload_database()
```

### 创建自定义效果处理器

```gdscript
# 注册自定义标签处理器
TagUtil.register_handler("custom_effect", func(service, item, value):
    # 处理自定义效果
    print("触发自定义效果: %s" % value)
    service.modify_stat("custom_stat", value)
)
```

### 使用物品

```gdscript
# 通过背包管理器使用物品（推荐）
InventoryManager.use_item(slot_index)

# 或直接触发物品效果（调试用）
var item = ItemDatabase.get_item("potion_health")
if TagUtil.is_usable(item):
    GameManager.item_effect_service._on_item_used(item)
```

### 查询玩家属性

```gdscript
# 通过StatService查询（推荐）
var hp = GameManager.stat_service.get_stat("hp")
var is_dead = GameManager.stat_service.is_dead()

# 或通过兼容接口
var player_data = GameManager.get_player_data()
```

## 测试和调试

### 运行集成测试
```gdscript
# 在场景中添加修复版测试脚本
var test_node = preload("res://Script/TestScript/inventory_refactor_test.gd").new()
add_child(test_node)
```

### 调试命令
```gdscript
# 打印服务状态
GameManager.debug_service_status()

# 测试标签系统
GameManager.test_tag_system()

# 验证服务依赖
var item_effect = GameManager.item_effect_service
print("StatService引用: %s" % ("✅" if item_effect.stat_service else "❌"))

# 打印物品标签信息（使用统一格式）
TagUtil.debug_print_item_tags(item)

# 打印玩家属性
GameManager.stat_service.debug_print_stats()
```

## 架构设计详解

### 工具类设计模式
- **静态工具类**: `ItemDatabase` 和 `TagUtil` 使用纯静态方法
- **无实例化**: 不需要创建实例，直接调用类方法
- **全局访问**: 通过 `class_name` 在任何地方直接使用

### 服务层架构
- **依赖注入**: 服务通过 GameManager 获取其他服务引用
- **生命周期管理**: 服务作为 GameManager 的子节点管理
- **信号通信**: 服务间通过信号进行松耦合通信

### 数据流向
```
JSON数据 → ItemDatabase(静态加载) → InventoryManager(CRUD) → ItemEffectService(效果处理) → StatService(属性更新) → UI更新
```

## 性能特性

### 优化措施
1. **静态工具类** - 避免不必要的实例创建
2. **标签缓存** - 避免重复解析
3. **懒加载** - 按需初始化服务和数据
4. **批量操作** - 支持批量属性更新
5. **内存管理** - 自动清理无用数据

### 扩展性
1. **插件架构** - 通过处理器注册扩展
2. **数据驱动** - JSON配置支持热更新
3. **模块化** - 服务层独立可测试
4. **向后兼容** - 保持原有API接口

## 故障排除

### 常见问题及解决方案

1. **工具类调用错误**
   ```gdscript
   # ❌ 错误：尝试实例化工具类
   var db = ItemDatabase.new()  # 错误！
   
   # ✅ 正确：直接调用静态方法
   var item = ItemDatabase.get_item("potion_health")
   ```

2. **服务未初始化**
   ```gdscript
   # 检查服务状态
   if not GameManager.stat_service:
       print("StatService未初始化")
   ```

3. **标签解析错误**
   ```gdscript
   # 验证标签格式
   var tags = TagUtil._get_parsed_tags(item)
   print("解析后标签: %s" % tags)
   ```

4. **物品效果不生效**
   ```gdscript
   # 检查处理器注册
   print("已注册处理器: %s" % TagUtil.get_registered_handlers())
   ```

### 调试工具
- `ItemDatabase.validate_database()` - 验证数据完整性
- `GameManager.item_effect_service.debug_print_status()` - 检查服务状态
- `GameManager.stat_service.debug_print_stats()` - 查看当前属性

## 后续扩展

### 计划功能
1. **BuffService** - 临时效果和状态管理
2. **SetBonus** - 装备套装效果系统
3. **QuestIntegration** - 深度任务系统集成
4. **CraftingSystem** - 物品制作和合成

### 高级特性
- `duration:N` - 时间限制效果
- `condition:stat:value` - 条件触发
- `transform:item_id` - 物品转换
- `aura:range:effect` - 范围效果

**最新版**已实现关键架构优化：将 `ItemDatabase` 和 `TagUtil` 改为纯静态工具类，消除了单例依赖问题，提升了系统性能和代码清晰度。系统现在具备生产就绪的质量和良好的扩展性。