# Wake - 基础Git操作指南

## 首次克隆项目
```bash
git clone <项目地址>
cd wake
```

### 配置Git用户信息
```bash
# 配置用户名和邮箱（必须，否则无法提交）
git config user.name "你的用户名"
git config user.email "你的邮箱@example.com"

# 验证配置
git config user.name
git config user.email
```

**获取Gitee邮箱地址：**
1. 登录Gitee账号
2. 点击右上角头像 → 设置
3. 左侧菜单选择"邮箱管理"
4. 查看你的主邮箱或添加新邮箱

## 修改代码流程

### 1. 更新本地代码
```bash
git pull origin master
```

### 2. 在Godot中修改代码
- 打开Godot编辑器
- 完成所有代码修改
- 在Godot中测试确保功能正常

### 3. 提交修改
```bash
# 查看修改状态
git status

# 添加所有修改文件
git add .

# 提交修改（用有意义的描述）
git commit -m "简洁描述你的修改内容"
```

## 提交Pull Request

### 1. 推送到远程仓库
```bash
git push origin master
```

### 2. 在GitHub/GitLab上创建PR
- 访问项目页面
- 点击"New Pull Request"
- 填写PR标题和描述
- 提交PR等待审核

## 常用命令速查
```bash
git status          # 查看文件状态
git add .           # 添加所有修改
git commit -m "..."  # 提交修改
git push            # 推送到远程
git pull            # 拉取最新代码
```

## 新手建议
- 每次在Godot中完成一个功能后立即提交一次commit
- commit信息要简洁明了，描述做了什么
- 推送前先pull确保代码是最新的