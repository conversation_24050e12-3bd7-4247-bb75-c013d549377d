[gd_resource type="Theme" load_steps=13 format=3 uid="uid://xoalsbenvoe6"]

[sub_resource type="FontFile" id="FontFile_1"]
oversampling = 2.0
cache/0/14/0/ascent = 0.0
cache/0/14/0/descent = 0.0
cache/0/14/0/underline_position = 0.0
cache/0/14/0/underline_thickness = 0.0
cache/0/14/0/scale = 1.0
cache/0/16/0/ascent = 0.0
cache/0/16/0/descent = 0.0
cache/0/16/0/underline_position = 0.0
cache/0/16/0/underline_thickness = 0.0
cache/0/16/0/scale = 1.0
cache/0/18/0/ascent = 0.0
cache/0/18/0/descent = 0.0
cache/0/18/0/underline_position = 0.0
cache/0/18/0/underline_thickness = 0.0
cache/0/18/0/scale = 1.0
cache/0/24/0/ascent = 0.0
cache/0/24/0/descent = 0.0
cache/0/24/0/underline_position = 0.0
cache/0/24/0/underline_thickness = 0.0
cache/0/24/0/scale = 1.0
cache/0/20/0/ascent = 0.0
cache/0/20/0/descent = 0.0
cache/0/20/0/underline_position = 0.0
cache/0/20/0/underline_thickness = 0.0
cache/0/20/0/scale = 1.0
cache/0/15/0/ascent = 0.0
cache/0/15/0/descent = 0.0
cache/0/15/0/underline_position = 0.0
cache/0/15/0/underline_thickness = 0.0
cache/0/15/0/scale = 1.0
cache/0/13/0/ascent = 0.0
cache/0/13/0/descent = 0.0
cache/0/13/0/underline_position = 0.0
cache/0/13/0/underline_thickness = 0.0
cache/0/13/0/scale = 1.0
cache/0/12/0/ascent = 0.0
cache/0/12/0/descent = 0.0
cache/0/12/0/underline_position = 0.0
cache/0/12/0/underline_thickness = 0.0
cache/0/12/0/scale = 1.0
cache/0/21/0/ascent = 0.0
cache/0/21/0/descent = 0.0
cache/0/21/0/underline_position = 0.0
cache/0/21/0/underline_thickness = 0.0
cache/0/21/0/scale = 1.0
cache/0/22/0/ascent = 0.0
cache/0/22/0/descent = 0.0
cache/0/22/0/underline_position = 0.0
cache/0/22/0/underline_thickness = 0.0
cache/0/22/0/scale = 1.0
cache/0/23/0/ascent = 0.0
cache/0/23/0/descent = 0.0
cache/0/23/0/underline_position = 0.0
cache/0/23/0/underline_thickness = 0.0
cache/0/23/0/scale = 1.0
cache/0/32/0/ascent = 0.0
cache/0/32/0/descent = 0.0
cache/0/32/0/underline_position = 0.0
cache/0/32/0/underline_thickness = 0.0
cache/0/32/0/scale = 1.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_10"]
bg_color = Color(0.3, 0.3, 0.35, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_3"]
bg_color = Color(0.3, 0.4, 0.6, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.5, 0.6, 0.8, 0.8)
corner_radius_top_left = 6
corner_radius_top_right = 6
corner_radius_bottom_right = 6
corner_radius_bottom_left = 6

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2"]
bg_color = Color(0.2, 0.25, 0.35, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.4, 0.5, 0.7, 0.6)
corner_radius_top_left = 6
corner_radius_top_right = 6
corner_radius_bottom_right = 6
corner_radius_bottom_left = 6

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4"]
bg_color = Color(0.4, 0.5, 0.7, 1)
corner_radius_top_left = 6
corner_radius_top_right = 6
corner_radius_bottom_right = 6
corner_radius_bottom_left = 6

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_11"]
bg_color = Color(0.12, 0.12, 0.18, 0.9)
border_width_bottom = 2
border_color = Color(0.4, 0.5, 0.7, 0.8)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_8"]
bg_color = Color(0.2, 0.6, 0.9, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_9"]
bg_color = Color(0.15, 0.45, 0.7, 1)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_7"]
bg_color = Color(0.18, 0.22, 0.3, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.4, 0.5, 0.7, 0.4)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6"]
bg_color = Color(0.35, 0.45, 0.65, 1)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_5"]
bg_color = Color(0.25, 0.3, 0.4, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.4, 0.5, 0.7, 0.6)
corner_radius_top_left = 4
corner_radius_top_right = 4
corner_radius_bottom_right = 4
corner_radius_bottom_left = 4

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.15, 0.15, 0.2, 0.95)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.3, 0.4, 0.6, 0.8)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[resource]
Button/colors/font_color = Color(0.9, 0.9, 0.95, 1)
Button/colors/font_disabled_color = Color(0.5, 0.5, 0.5, 1)
Button/colors/font_hover_color = Color(1, 1, 1, 1)
Button/colors/font_pressed_color = Color(0.8, 0.9, 1, 1)
Button/font_sizes/font_size = 20
Button/fonts/font = SubResource("FontFile_1")
Button/styles/disabled = SubResource("StyleBoxFlat_10")
Button/styles/hover = SubResource("StyleBoxFlat_3")
Button/styles/normal = SubResource("StyleBoxFlat_2")
Button/styles/pressed = SubResource("StyleBoxFlat_4")
CheckBox/colors/font_color = Color(0.9, 0.9, 0.95, 1)
CheckBox/colors/font_hover_color = Color(1, 1, 1, 1)
CheckBox/font_sizes/font_size = 16
CheckBox/fonts/font = SubResource("FontFile_1")
HSeparator/constants/separation = 8
HSeparator/styles/separator = SubResource("StyleBoxFlat_11")
HSlider/colors/font_color = Color(0.9, 0.9, 0.95, 1)
HSlider/font_sizes/font_size = 16
HSlider/fonts/font = SubResource("FontFile_1")
HSlider/styles/grabber_area = SubResource("StyleBoxFlat_8")
HSlider/styles/grabber_area_highlight = SubResource("StyleBoxFlat_9")
HSlider/styles/slider = SubResource("StyleBoxFlat_7")
Label/colors/font_color = Color(0.9, 0.9, 0.95, 1)
Label/font_sizes/font_size = 20
Label/fonts/font = SubResource("FontFile_1")
OptionButton/colors/font_color = Color(0.9, 0.9, 0.95, 1)
OptionButton/colors/font_hover_color = Color(1, 1, 1, 1)
OptionButton/colors/font_pressed_color = Color(0.8, 0.9, 1, 1)
OptionButton/font_sizes/font_size = 16
OptionButton/fonts/font = SubResource("FontFile_1")
OptionButton/styles/hover = SubResource("StyleBoxFlat_6")
OptionButton/styles/normal = SubResource("StyleBoxFlat_5")
OptionButton/styles/pressed = SubResource("StyleBoxFlat_6")
Panel/styles/panel = SubResource("StyleBoxFlat_1")
ScrollContainer/styles/bg = SubResource("StyleBoxFlat_7")
SpinBox/colors/font_color = Color(0.9, 0.9, 0.95, 1)
SpinBox/font_sizes/font_size = 16
SpinBox/fonts/font = SubResource("FontFile_1")
