shader_type canvas_item;

// ---- 统一控制与裂纹参数 ----
uniform float progress : hint_range(0.0, 1.0) = 0.0;
uniform float shatter_start_time : hint_range(0.0, 1.0) = 0.5;

// ---- NEW: Realism Parameters ----
uniform vec2 impact_point = vec2(0.5, 0.5); // The point where the shatter originates
uniform vec4 edge_color : source_color = vec4(0.4, 0.4, 0.4, 1.0); // Color of the broken edges for 3D feel
uniform float impact_fuzziness : hint_range(0.0, 0.5) = 0.2; // Randomness in crack propagation speed

// Crack generation parameters
uniform float crack_depth : hint_range(0.0, 10.0) = 2.236;
uniform float crack_scale : hint_range(1.0, 10.0) = 4.0;
uniform float crack_zebra_scale : hint_range(0.0, 10.0) = 2.67;
uniform float crack_zebra_amp : hint_range(0.0, 10.0) = 1.3;
uniform float crack_profile : hint_range(0.0, 10.0) = 1.0;
uniform float crack_slope : hint_range(0.0, 50.0) = 45.8;
uniform float crack_width : hint_range(0.0, 1.0) = 0.001;
uniform vec4 crack_color : source_color = vec4(0.0, 0.0, 0.0, 1.0);

// Shatter effect parameters
uniform float shatter_strength : hint_range(0.0, 2.0) = 0.4;
uniform float shatter_gravity : hint_range(0.0, 5.0) = 1.5;
uniform float rotation_strength : hint_range(0.0, 5.0) = 1.0;

// Add these two new uniforms with your other uniforms at the top of the file.
// ---- NEW: Shatter Animation Parameters ----
uniform float shatter_direction_randomness : hint_range(0.0, 2.0) = 0.8; // How much randomness to add to the shatter direction. 0.0 = perfectly outwards.
uniform float shatter_delay : hint_range(0.0, 1.0) = 0.4; // How much the shatter effect propagates outwards. 0.0 = all at once.

// ---- 辅助函数 (Helper Functions) ----
vec2 hash(vec2 p) {
    p = vec2(dot(p, vec2(127.1, 311.7)), dot(p, vec2(269.5, 183.3)));
    return fract(sin(p) * 43758.5453123);
}

float noise(vec2 p) {
    vec2 i = floor(p);
    vec2 f = fract(p);
    f = f * f * (3.0 - 2.0 * f);
    return mix(mix(dot(hash(i + vec2(0.0, 0.0)), f - vec2(0.0, 0.0)),
                   dot(hash(i + vec2(1.0, 0.0)), f - vec2(1.0, 0.0)), f.x),
               mix(dot(hash(i + vec2(0.0, 1.0)), f - vec2(0.0, 1.0)),
                   dot(hash(i + vec2(1.0, 1.0)), f - vec2(1.0, 1.0)), f.x), f.y);
}

vec3 voronoiB(vec2 u) {
    vec2 p = floor(u);
    vec2 f = fract(u);
    float res = 8.0;
    vec2 C = vec2(0.0);
    vec2 P = vec2(0.0);
    for (int j = -1; j <= 1; j++) {
        for (int i = -1; i <= 1; i++) {
            vec2 g = vec2(float(i), float(j));
            vec2 o = hash(p + g);
            vec2 r = g - f + o;
            float d = dot(r, r);
            if (d < res) {
                res = d;
                C = g;
                P = r;
            }
        }
    }
    res = 8.0;
    for (int j = -2; j <= 2; j++) {
        for (int i = -2; i <= 2; i++) {
            vec2 g = C + vec2(float(i), float(j));
            vec2 o = hash(p + g);
            vec2 r = g - f + o;
            if (dot(P - r, P - r) > 1e-5) {
                res = min(res, 0.5 * dot((P + r), normalize(r - P)));
            }
        }
    }
    return vec3(res, p + C);
}

float fbm(vec2 n) {
    float total = 0.0, amp = 1.0;
    for (int i = 0; i < 7; i++) {
        total += noise(n) * amp;
        n += n;
        amp *= 0.5;
    }
    return total;
}

float generate_crack_alpha(vec2 uv) {
	vec2 U = uv * crack_scale;
    float alpha = 0.0;
    for (float i = 0.0; i < crack_depth; i++) {
        vec2 D = vec2(crack_zebra_amp * fbm(U / crack_zebra_scale) * crack_zebra_scale);
        vec3 H = voronoiB(U + D);
        float d = H.x;
        d = min(1.0, crack_slope * pow(max(0.0, d - crack_width), crack_profile));
        alpha += (1.0 - d) / exp2(i);
    }
	return min(alpha, 1.0);
}

// ---- 主函数 FRAGMENT ----
void fragment() {
    // --- 1. 获取碎片信息 ---
	vec3 cell_info = voronoiB(UV * crack_scale);
	vec2 cell_id = cell_info.yz;
    vec2 cell_center_uv = (cell_id + hash(cell_id)) / crack_scale;

	// --- 2. 动画阶段判断 ---
	if (progress < shatter_start_time) {
        // --- STAGE 1: CRACKING ---
        float crack_progress = progress / shatter_start_time;
        float dist_from_impact = distance(cell_center_uv, impact_point);
        float cell_random_threshold = dist_from_impact - hash(cell_id + 0.5).x * impact_fuzziness;

        if (crack_progress < cell_random_threshold) {
            COLOR = texture(TEXTURE, UV);
        } else {
            float crack_alpha = generate_crack_alpha(UV);
            vec4 base_color = texture(TEXTURE, UV);
            COLOR = mix(base_color, crack_color, crack_alpha);
        }

	} else {
        // --- STAGE 2: SHATTERING ---
        float shatter_progress = (progress - shatter_start_time) / (1.0 - shatter_start_time);
        float dist_from_impact = distance(cell_center_uv, impact_point);
        float delay = dist_from_impact * shatter_delay;
        float shard_progress = clamp((shatter_progress - delay) / (1.0 - delay), 0.0, 1.0);

        // --- THIS IS THE CORRECTED LOGIC STRUCTURE ---
        if (shard_progress <= 0.0) {
            // This shard has not started moving yet.
            // Show the cracked texture without transformation.
            float crack_alpha = generate_crack_alpha(UV);
            vec4 base_color = texture(TEXTURE, UV);
            COLOR = mix(base_color, crack_color, crack_alpha);
        } else {
            // This shard is actively moving.
            // The logic that was previously after the 'return' is now inside this 'else' block.

            float eased_progress = pow(shard_progress, 1.8);
            vec2 direction_from_impact = normalize(cell_center_uv - impact_point);
            vec2 random_direction = (hash(cell_id) - 0.5) * 2.0;
            vec2 final_direction = normalize(mix(direction_from_impact, random_direction, shatter_direction_randomness));
            float mass = 0.7 + hash(cell_id).y * 0.6;

            vec2 offset = final_direction * eased_progress * shatter_strength / mass;
            offset.y += eased_progress * eased_progress * shatter_gravity / mass;
            float rotation_direction = (hash(cell_id + 0.1).x - 0.5) * 2.0;
            float rotation_angle = rotation_direction * eased_progress * rotation_strength * 3.14159 / mass;
            vec2 centered_uv = UV - cell_center_uv;
            mat2 rotation_matrix = mat2(vec2(cos(rotation_angle), -sin(rotation_angle)), vec2(sin(rotation_angle), cos(rotation_angle)));
            vec2 rotated_uv = centered_uv * rotation_matrix + cell_center_uv;
            vec2 final_uv = rotated_uv - offset;
            vec4 final_color = texture(TEXTURE, final_uv);

            float lighting = clamp(dot(normalize(final_direction), vec2(-0.707, -0.707)), 0.0, 1.0);
            vec4 lit_edge_color = edge_color * (0.6 + 0.8 * lighting);
            lit_edge_color.a = edge_color.a;
            float crack_alpha = generate_crack_alpha(final_uv);
            final_color = mix(final_color, lit_edge_color, crack_alpha);
            final_color.a *= (1.0 - eased_progress);
            if (final_uv.x < 0.0 || final_uv.x > 1.0 || final_uv.y < 0.0 || final_uv.y > 1.0) {
                discard;
            }
            COLOR = final_color;
        }
	}
}
