[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://i46x3c4p6ijx"
path="res://.godot/imported/OpenSans-VariableFont_wdth,wght.ttf-dc261889ba81066b5ec583fab4b6f584.fontdata"

[deps]

source_file="res://Asset/OpenSans-VariableFont_wdth,wght.ttf"
dest_files=["res://.godot/imported/OpenSans-VariableFont_wdth,wght.ttf-dc261889ba81066b5ec583fab4b6f584.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
