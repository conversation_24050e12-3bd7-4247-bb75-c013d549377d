shader_type canvas_item;

uniform float time_hour : hint_range(0.0, 23.0) = 12.0;
uniform float transition_speed : hint_range(0.1, 5.0) = 1.0;

// 时间段颜色定义
const vec3 dawn_color = vec3(1.0, 0.7, 0.4);        // 黎明 - 橙黄色
const vec3 morning_color = vec3(0.9, 0.95, 1.0);     // 上午 - 清澈蓝白
const vec3 noon_color = vec3(0.95, 0.98, 1.0);       // 正午 - 透亮天蓝白
const vec3 afternoon_color = vec3(1.0, 0.92, 0.8);   // 下午 - 温暖透亮
const vec3 sunset_color = vec3(1.0, 0.5, 0.2);       // 黄昏 - 橙红色
const vec3 dusk_color = vec3(0.4, 0.3, 0.6);         // 傍晚 - 紫蓝色
const vec3 night_color = vec3(0.1, 0.1, 0.3);        // 夜晚 - 深蓝色
const vec3 midnight_color = vec3(0.05, 0.05, 0.2);   // 深夜 - 极深蓝色

float get_time_alpha(float hour) {
    // 根据时间计算透明度 - 整体减弱约10%，让效果更微妙
    if (hour >= 6.0 && hour <= 18.0) {
        // 白天时间，透明度很低，让画面保持清晰透亮
        if (hour >= 8.0 && hour <= 16.0) {
            // 上午到下午时段，最低透明度实现清晰效果
            return mix(0.045, 0.018, smoothstep(8.0, 12.0, hour)) + 
                   mix(0.018, 0.072, smoothstep(12.0, 16.0, hour));
        } else {
            // 早晨和傍晚时段
            return mix(0.135, 0.045, smoothstep(6.0, 8.0, hour)) + 
                   mix(0.072, 0.225, smoothstep(16.0, 18.0, hour));
        }
    } else {
        // 夜晚时间，透明度较高
        if (hour < 6.0) {
            return mix(0.54, 0.135, smoothstep(0.0, 6.0, hour));
        } else {
            return mix(0.225, 0.54, smoothstep(18.0, 24.0, hour));
        }
    }
}

vec3 get_time_color(float hour) {
    vec3 color;
    
    if (hour < 5.0) {
        // 0-5点：深夜到黎明前
        color = mix(midnight_color, night_color, smoothstep(0.0, 3.0, hour));
        color = mix(color, dawn_color, smoothstep(3.0, 5.0, hour));
    } else if (hour < 8.0) {
        // 5-8点：黎明到上午
        color = mix(dawn_color, morning_color, smoothstep(5.0, 8.0, hour));
    } else if (hour < 12.0) {
        // 8-12点：上午到正午
        color = mix(morning_color, noon_color, smoothstep(8.0, 12.0, hour));
    } else if (hour < 16.0) {
        // 12-16点：正午到下午
        color = mix(noon_color, afternoon_color, smoothstep(12.0, 16.0, hour));
    } else if (hour < 18.0) {
        // 16-18点：下午到黄昏
        color = mix(afternoon_color, sunset_color, smoothstep(16.0, 18.0, hour));
    } else if (hour < 20.0) {
        // 18-20点：黄昏到傍晚
        color = mix(sunset_color, dusk_color, smoothstep(18.0, 20.0, hour));
    } else if (hour < 22.0) {
        // 20-22点：傍晚到夜晚
        color = mix(dusk_color, night_color, smoothstep(20.0, 22.0, hour));
    } else {
        // 22-24点：夜晚到深夜
        color = mix(night_color, midnight_color, smoothstep(22.0, 24.0, hour));
    }
    
    return color;
}

void fragment() {
    vec3 time_tint = get_time_color(time_hour);
    float alpha = get_time_alpha(time_hour);
    
    // 使用乘法混合模式模拟光照效果
    COLOR = vec4(time_tint, alpha);
}