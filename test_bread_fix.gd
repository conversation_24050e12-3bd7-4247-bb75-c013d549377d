extends Node

# 测试面包堆叠使用的修复
func _ready():
	print("=== 测试面包堆叠使用修复 ===")
	
	# 初始化必要的管理器
	if not InventoryManager:
		print("错误：InventoryManager 未初始化")
		return
	
	if not ItemDatabase:
		print("错误：ItemDatabase 未初始化")
		return
	
	# 初始化数据库
	ItemDatabase.initialize()
	
	# 清空背包
	InventoryManager.clear_inventory()
	
	# 添加10个面包
	print("添加10个面包...")
	var success = InventoryManager.add_item("bread", 10)
	print("添加结果: %s" % success)
	
	# 检查背包状态
	print("\n=== 背包状态 ===")
	for i in range(InventoryManager.inventory_slots.size()):
		var slot = InventoryManager.inventory_slots[i]
		if not slot.is_empty():
			print("槽位 %d: %s x%d" % [i, slot.get_template().name, slot.get_quantity()])
			# 检查每个实例的使用次数
			for j in range(slot.item_instances.size()):
				var instance = slot.item_instances[j]
				print("  实例 %d: 剩余使用次数 %d" % [j, instance.get_remaining_uses()])
	
	# 测试使用面包
	print("\n=== 测试使用面包 ===")
	var bread_slot_index = -1
	for i in range(InventoryManager.inventory_slots.size()):
		var slot = InventoryManager.inventory_slots[i]
		if not slot.is_empty() and slot.template_id == "bread":
			bread_slot_index = i
			break
	
	if bread_slot_index == -1:
		print("错误：找不到面包槽位")
		return
	
	# 连续使用5次面包
	for i in range(5):
		print("\n--- 第 %d 次使用面包 ---" % (i + 1))
		var slot = InventoryManager.inventory_slots[bread_slot_index]
		
		print("使用前：槽位有 %d 个面包" % slot.get_quantity())
		if slot.get_quantity() > 0:
			print("第一个实例剩余使用次数：%d" % slot.item_instances[0].get_remaining_uses())
		
		var use_result = InventoryManager.use_item(bread_slot_index)
		print("使用结果: %s" % use_result)
		
		print("使用后：槽位有 %d 个面包" % slot.get_quantity())
		if slot.get_quantity() > 0:
			print("第一个实例剩余使用次数：%d" % slot.item_instances[0].get_remaining_uses())
		
		if slot.get_quantity() == 0:
			print("面包用完了！")
			break
	
	print("\n=== 测试完成 ===")
	
	# 退出
	get_tree().quit()
