[gd_scene load_steps=8 format=3 uid="uid://v1qb8n3fgy41"]

[ext_resource type="Texture2D" uid="uid://cqtmwp1fu3u0f" path="res://Asset/school_main_scene.png" id="1_23kt7"]
[ext_resource type="Script" uid="uid://m8y4bmj7jd2j" path="res://Script/Map/main_school.gd" id="1_l8sjr"]
[ext_resource type="PackedScene" uid="uid://dvtdgo0n3gkyq" path="res://Scene/Player/test_runner.tscn" id="2_esjea"]
[ext_resource type="Texture2D" uid="uid://c7gpjdiomn2pg" path="res://Asset/Inventory/coin.png" id="3_igpbt"]
[ext_resource type="Script" uid="uid://423ovu1224jg" path="res://Script/Map/coin_point.gd" id="4_coin_script"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_esjea"]
size = Vector2(262.3, 99.6399)

[sub_resource type="CircleShape2D" id="CircleShape2D_yatvv"]
radius = 29.0689

[node name="MainSchool" type="Node2D"]
script = ExtResource("1_l8sjr")

[node name="Background" type="Sprite2D" parent="."]
z_index = -1
position = Vector2(2304, 1536)
scale = Vector2(3, 3)
texture = ExtResource("1_23kt7")

[node name="CollisionObstacles" type="Node2D" parent="."]

[node name="StaticBody2D" type="StaticBody2D" parent="CollisionObstacles"]

[node name="CollisionShape2D" type="CollisionShape2D" parent="CollisionObstacles/StaticBody2D"]
position = Vector2(1822.05, 2217.41)
rotation = -0.397935
shape = SubResource("RectangleShape2D_esjea")

[node name="TestRunner" parent="." instance=ExtResource("2_esjea")]
position = Vector2(1984, 2496)

[node name="CoinPoint" type="Area2D" parent="."]
position = Vector2(2432, 1920)
script = ExtResource("4_coin_script")

[node name="Sprite2D" type="Sprite2D" parent="CoinPoint"]
scale = Vector2(0.5, 0.5)
texture = ExtResource("3_igpbt")

[node name="CollisionShape2D" type="CollisionShape2D" parent="CoinPoint"]
shape = SubResource("CircleShape2D_yatvv")
