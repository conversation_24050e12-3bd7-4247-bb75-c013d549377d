[gd_scene load_steps=4 format=3 uid="uid://xb6br60jv2f4"]

[ext_resource type="Script" uid="uid://c0tk3khsl5kft" path="res://Script/UI/settings_ui.gd" id="1_1japj"]
[ext_resource type="Theme" uid="uid://xoalsbenvoe6" path="res://Asset/main_theme.tres" id="2_theme"]

[sub_resource type="FontFile" id="FontFile_1"]
subpixel_positioning = 0
msdf_pixel_range = 14
msdf_size = 128
oversampling = 2.0
cache/0/24/0/ascent = 0.0
cache/0/24/0/descent = 0.0
cache/0/24/0/underline_position = 0.0
cache/0/24/0/underline_thickness = 0.0
cache/0/24/0/scale = 1.0
cache/0/16/0/ascent = 0.0
cache/0/16/0/descent = 0.0
cache/0/16/0/underline_position = 0.0
cache/0/16/0/underline_thickness = 0.0
cache/0/16/0/scale = 1.0
cache/0/18/0/ascent = 0.0
cache/0/18/0/descent = 0.0
cache/0/18/0/underline_position = 0.0
cache/0/18/0/underline_thickness = 0.0
cache/0/18/0/scale = 1.0
cache/0/28/0/ascent = 0.0
cache/0/28/0/descent = 0.0
cache/0/28/0/underline_position = 0.0
cache/0/28/0/underline_thickness = 0.0
cache/0/28/0/scale = 1.0
cache/0/19/0/ascent = 0.0
cache/0/19/0/descent = 0.0
cache/0/19/0/underline_position = 0.0
cache/0/19/0/underline_thickness = 0.0
cache/0/19/0/scale = 1.0
cache/0/20/0/ascent = 0.0
cache/0/20/0/descent = 0.0
cache/0/20/0/underline_position = 0.0
cache/0/20/0/underline_thickness = 0.0
cache/0/20/0/scale = 1.0
cache/0/21/0/ascent = 0.0
cache/0/21/0/descent = 0.0
cache/0/21/0/underline_position = 0.0
cache/0/21/0/underline_thickness = 0.0
cache/0/21/0/scale = 1.0
cache/0/22/0/ascent = 0.0
cache/0/22/0/descent = 0.0
cache/0/22/0/underline_position = 0.0
cache/0/22/0/underline_thickness = 0.0
cache/0/22/0/scale = 1.0

[node name="SettingsUI" type="CanvasLayer"]
script = ExtResource("1_1japj")

[node name="Background" type="ColorRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 0.7)

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("2_theme")

[node name="Panel" type="Panel" parent="Control"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -450.0
offset_top = -350.0
offset_right = 450.0
offset_bottom = 350.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Control/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = 25.0
offset_right = -30.0
offset_bottom = -25.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleLabel" type="Label" parent="Control/Panel/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.9, 0.95, 1, 1)
theme_override_fonts/font = SubResource("FontFile_1")
theme_override_font_sizes/font_size = 28
text = "设置"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="Control/Panel/VBoxContainer"]
layout_mode = 2

[node name="ScrollContainer" type="ScrollContainer" parent="Control/Panel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="SettingsVBox" type="VBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 12

[node name="AudioGroup" type="VBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox"]
layout_mode = 2
theme_override_constants/separation = 8

[node name="AudioLabel" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/AudioGroup"]
layout_mode = 2
theme_override_colors/font_color = Color(0.8, 0.9, 1, 1)
theme_override_fonts/font = SubResource("FontFile_1")
theme_override_font_sizes/font_size = 22
text = "音频设置"

[node name="MasterVolumeGroup" type="HBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/AudioGroup"]
layout_mode = 2

[node name="MasterVolumeLabel" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/AudioGroup/MasterVolumeGroup"]
layout_mode = 2
size_flags_horizontal = 3
text = "主音量"

[node name="MasterVolumeSlider" type="HSlider" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/AudioGroup/MasterVolumeGroup"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
max_value = 1.0
step = 0.01
value = 1.0

[node name="MasterVolumeValue" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/AudioGroup/MasterVolumeGroup"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 0
text = "100%"

[node name="MusicVolumeGroup" type="HBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/AudioGroup"]
layout_mode = 2

[node name="MusicVolumeLabel" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/AudioGroup/MusicVolumeGroup"]
layout_mode = 2
size_flags_horizontal = 3
text = "音乐音量"

[node name="MusicVolumeSlider" type="HSlider" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/AudioGroup/MusicVolumeGroup"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
max_value = 1.0
step = 0.01
value = 0.8

[node name="MusicVolumeValue" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/AudioGroup/MusicVolumeGroup"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 0
text = "80%"

[node name="SfxVolumeGroup" type="HBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/AudioGroup"]
layout_mode = 2

[node name="SfxVolumeLabel" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/AudioGroup/SfxVolumeGroup"]
layout_mode = 2
size_flags_horizontal = 3
text = "音效音量"

[node name="SfxVolumeSlider" type="HSlider" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/AudioGroup/SfxVolumeGroup"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
max_value = 1.0
step = 0.01
value = 0.8

[node name="SfxVolumeValue" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/AudioGroup/SfxVolumeGroup"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 0
text = "80%"

[node name="AudioSeparator" type="HSeparator" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox"]
layout_mode = 2

[node name="DisplayGroup" type="VBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox"]
layout_mode = 2
theme_override_constants/separation = 8

[node name="DisplayLabel" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/DisplayGroup"]
layout_mode = 2
theme_override_colors/font_color = Color(0.8, 0.9, 1, 1)
theme_override_fonts/font = SubResource("FontFile_1")
theme_override_font_sizes/font_size = 22
text = "显示设置"

[node name="FullscreenGroup" type="HBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/DisplayGroup"]
layout_mode = 2

[node name="FullscreenLabel" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/DisplayGroup/FullscreenGroup"]
layout_mode = 2
size_flags_horizontal = 3
text = "全屏模式"

[node name="FullscreenCheckBox" type="CheckBox" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/DisplayGroup/FullscreenGroup"]
unique_name_in_owner = true
layout_mode = 2

[node name="VsyncGroup" type="HBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/DisplayGroup"]
layout_mode = 2

[node name="VsyncLabel" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/DisplayGroup/VsyncGroup"]
layout_mode = 2
size_flags_horizontal = 3
text = "垂直同步"

[node name="VsyncCheckBox" type="CheckBox" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/DisplayGroup/VsyncGroup"]
unique_name_in_owner = true
layout_mode = 2
button_pressed = true

[node name="ResolutionGroup" type="HBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/DisplayGroup"]
layout_mode = 2

[node name="ResolutionLabel" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/DisplayGroup/ResolutionGroup"]
layout_mode = 2
size_flags_horizontal = 3
text = "分辨率"

[node name="ResolutionOptionButton" type="OptionButton" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/DisplayGroup/ResolutionGroup"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3

[node name="WindowModeGroup" type="HBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/DisplayGroup"]
layout_mode = 2

[node name="WindowModeLabel" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/DisplayGroup/WindowModeGroup"]
layout_mode = 2
size_flags_horizontal = 3
text = "窗口模式"

[node name="WindowModeOptionButton" type="OptionButton" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/DisplayGroup/WindowModeGroup"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3

[node name="DisplaySeparator" type="HSeparator" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox"]
layout_mode = 2

[node name="GameGroup" type="VBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox"]
layout_mode = 2
theme_override_constants/separation = 8

[node name="GameLabel" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/GameGroup"]
layout_mode = 2
theme_override_colors/font_color = Color(0.8, 0.9, 1, 1)
theme_override_fonts/font = SubResource("FontFile_1")
theme_override_font_sizes/font_size = 22
text = "游戏设置"

[node name="LanguageGroup" type="HBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/GameGroup"]
layout_mode = 2

[node name="LanguageLabel" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/GameGroup/LanguageGroup"]
layout_mode = 2
size_flags_horizontal = 3
text = "语言"

[node name="LanguageOptionButton" type="OptionButton" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/GameGroup/LanguageGroup"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3

[node name="AutosaveGroup" type="HBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/GameGroup"]
visible = false
layout_mode = 2

[node name="AutosaveLabel" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/GameGroup/AutosaveGroup"]
layout_mode = 2
size_flags_horizontal = 3
text = "自动保存"

[node name="AutosaveCheckBox" type="CheckBox" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/GameGroup/AutosaveGroup"]
unique_name_in_owner = true
layout_mode = 2

[node name="AutosaveIntervalGroup" type="HBoxContainer" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/GameGroup"]
visible = false
layout_mode = 2

[node name="AutosaveIntervalLabel" type="Label" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/GameGroup/AutosaveIntervalGroup"]
layout_mode = 2
size_flags_horizontal = 3
text = "自动保存间隔"

[node name="AutosaveIntervalSpinBox" type="SpinBox" parent="Control/Panel/VBoxContainer/ScrollContainer/SettingsVBox/GameGroup/AutosaveIntervalGroup"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
min_value = 60.0
max_value = 600.0
value = 120.0
suffix = "秒"

[node name="ButtonsContainer" type="HBoxContainer" parent="Control/Panel/VBoxContainer"]
layout_mode = 2

[node name="ResetButton" type="Button" parent="Control/Panel/VBoxContainer/ButtonsContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "重置默认"

[node name="HSeparator2" type="HSeparator" parent="Control/Panel/VBoxContainer/ButtonsContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="ReturnButton" type="Button" parent="Control/Panel/VBoxContainer/ButtonsContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "返回"
