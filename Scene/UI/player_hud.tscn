[gd_scene load_steps=2 format=3 uid="uid://bf3j1wkxxn8y3"]

[ext_resource type="Script" uid="uid://ckprnaah232bv" path="res://Script/UI/player_hud.gd" id="1_7k2mh"]

[node name="PlayerHUD" type="CanvasLayer"]
process_mode = 3
layer = 100
script = ExtResource("1_7k2mh")

[node name="HUDContainer" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="TopLeft" type="VBoxContainer" parent="HUDContainer"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 1600.0
offset_top = -1016.0
offset_right = 1880.0
offset_bottom = -912.0
grow_vertical = 0

[node name="HPContainer" type="HBoxContainer" parent="HUDContainer/TopLeft"]
layout_mode = 2

[node name="HPLabel" type="Label" parent="HUDContainer/TopLeft/HPContainer"]
modulate = Color(1, 0.4, 0.4, 1)
layout_mode = 2
text = "HP:"

[node name="HPValue" type="Label" parent="HUDContainer/TopLeft/HPContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "100/100"

[node name="CoinsContainer" type="HBoxContainer" parent="HUDContainer/TopLeft"]
layout_mode = 2

[node name="CoinsLabel" type="Label" parent="HUDContainer/TopLeft/CoinsContainer"]
modulate = Color(1, 0.8, 0.2, 1)
layout_mode = 2
text = "金币:"

[node name="CoinsValue" type="Label" parent="HUDContainer/TopLeft/CoinsContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "0"

[node name="LevelContainer" type="HBoxContainer" parent="HUDContainer/TopLeft"]
layout_mode = 2

[node name="LevelLabel" type="Label" parent="HUDContainer/TopLeft/LevelContainer"]
modulate = Color(0.4, 1, 0.4, 1)
layout_mode = 2
text = "等级:"

[node name="LevelValue" type="Label" parent="HUDContainer/TopLeft/LevelContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "1"

[node name="ExperienceContainer" type="HBoxContainer" parent="HUDContainer/TopLeft"]
layout_mode = 2

[node name="ExperienceLabel" type="Label" parent="HUDContainer/TopLeft/ExperienceContainer"]
modulate = Color(0.4, 0.8, 1, 1)
layout_mode = 2
text = "经验:"

[node name="ExperienceValue" type="Label" parent="HUDContainer/TopLeft/ExperienceContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "0"

[node name="InventoryButton" type="Button" parent="HUDContainer/TopLeft"]
unique_name_in_owner = true
layout_mode = 2
text = "背包 (I)"
