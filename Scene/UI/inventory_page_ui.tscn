[gd_scene load_steps=2 format=3 uid="uid://bc1uwurah0jj4"]

[ext_resource type="Script" uid="uid://duuvoj64d6iuv" path="res://Script/UI/inventory_page_ui.gd" id="1_inventory_page"]

[node name="InventoryPageUI" type="CanvasLayer"]
process_mode = 3
layer = 500
script = ExtResource("1_inventory_page")

[node name="Background" type="ColorRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 1)

[node name="MainContainer" type="VBoxContainer" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 100.0
offset_top = 50.0
offset_right = -100.0
offset_bottom = -50.0

[node name="TitleBar" type="HBoxContainer" parent="MainContainer"]
layout_mode = 2

[node name="Title" type="Label" parent="MainContainer/TitleBar"]
layout_mode = 2
size_flags_horizontal = 3
text = "背包"
theme_override_font_sizes/font_size = 32
horizontal_alignment = 1
vertical_alignment = 1

[node name="CloseButton" type="Button" parent="MainContainer/TitleBar"]
unique_name_in_owner = true
layout_mode = 2
text = "返回 (ESC)"

[node name="HSeparator" type="HSeparator" parent="MainContainer"]
layout_mode = 2

[node name="ContentContainer" type="HBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="LeftPanel" type="VBoxContainer" parent="MainContainer/ContentContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 2.0

[node name="InventoryLabel" type="Label" parent="MainContainer/ContentContainer/LeftPanel"]
layout_mode = 2
text = "物品栏"
theme_override_font_sizes/font_size = 24
horizontal_alignment = 1

[node name="InventoryControls" type="HBoxContainer" parent="MainContainer/ContentContainer/LeftPanel"]
layout_mode = 2

[node name="OrganizeButton" type="Button" parent="MainContainer/ContentContainer/LeftPanel/InventoryControls"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "一键整理"

[node name="InventoryGrid" type="GridContainer" parent="MainContainer/ContentContainer/LeftPanel"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3
columns = 6

[node name="VSeparator" type="VSeparator" parent="MainContainer/ContentContainer"]
layout_mode = 2

[node name="RightPanel" type="VBoxContainer" parent="MainContainer/ContentContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 1.0

[node name="EquipmentLabel" type="Label" parent="MainContainer/ContentContainer/RightPanel"]
layout_mode = 2
text = "装备栏"
theme_override_font_sizes/font_size = 24
horizontal_alignment = 1

[node name="EquipmentContainer" type="VBoxContainer" parent="MainContainer/ContentContainer/RightPanel"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3

[node name="WeaponSlot" type="Panel" parent="MainContainer/ContentContainer/RightPanel/EquipmentContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="WeaponLabel" type="Label" parent="MainContainer/ContentContainer/RightPanel/EquipmentContainer/WeaponSlot"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "武器"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ArmorSlot" type="Panel" parent="MainContainer/ContentContainer/RightPanel/EquipmentContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ArmorLabel" type="Label" parent="MainContainer/ContentContainer/RightPanel/EquipmentContainer/ArmorSlot"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "护甲"
horizontal_alignment = 1
vertical_alignment = 1

[node name="AccessorySlot" type="Panel" parent="MainContainer/ContentContainer/RightPanel/EquipmentContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="AccessoryLabel" type="Label" parent="MainContainer/ContentContainer/RightPanel/EquipmentContainer/AccessorySlot"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "饰品"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ShoesSlot" type="Panel" parent="MainContainer/ContentContainer/RightPanel/EquipmentContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ShoesLabel" type="Label" parent="MainContainer/ContentContainer/RightPanel/EquipmentContainer/ShoesSlot"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "鞋子"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HelmetSlot" type="Panel" parent="MainContainer/ContentContainer/RightPanel/EquipmentContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="HelmetLabel" type="Label" parent="MainContainer/ContentContainer/RightPanel/EquipmentContainer/HelmetSlot"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "头盔"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HSeparator2" type="HSeparator" parent="MainContainer"]
layout_mode = 2

[node name="BottomPanel" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2

[node name="ItemInfo" type="VBoxContainer" parent="MainContainer/BottomPanel"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3

[node name="ItemName" type="Label" parent="MainContainer/BottomPanel/ItemInfo"]
unique_name_in_owner = true
layout_mode = 2
text = "选中物品信息"
theme_override_font_sizes/font_size = 20
horizontal_alignment = 1

[node name="ItemDescriptionContainer" type="ScrollContainer" parent="MainContainer/BottomPanel/ItemInfo"]
layout_mode = 2
custom_minimum_size = Vector2(0, 120)
size_flags_vertical = 3

[node name="ItemDescription" type="Label" parent="MainContainer/BottomPanel/ItemInfo/ItemDescriptionContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
text = "请选择一个物品查看详细信息"
autowrap_mode = 3
vertical_alignment = 0

[node name="ActionButtons" type="HBoxContainer" parent="MainContainer/BottomPanel"]
unique_name_in_owner = true
layout_mode = 2

[node name="UseButton" type="Button" parent="MainContainer/BottomPanel/ActionButtons"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "使用"

[node name="DropButton" type="Button" parent="MainContainer/BottomPanel/ActionButtons"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "丢弃"