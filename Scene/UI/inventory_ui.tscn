[gd_scene load_steps=2 format=3 uid="uid://bqk8xvnlxfhk3"]

[ext_resource type="Script" path="res://Script/UI/inventory_ui.gd" id="1_inventory"]

[node name="InventoryUI" type="CanvasLayer"]
process_mode = 3
layer = 200
script = ExtResource("1_inventory")

[node name="Background" type="ColorRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 0.7)

[node name="InventoryWindow" type="Panel" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -300.0
offset_right = 400.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="InventoryWindow"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="TitleBar" type="HBoxContainer" parent="InventoryWindow/VBoxContainer"]
layout_mode = 2

[node name="Title" type="Label" parent="InventoryWindow/VBoxContainer/TitleBar"]
layout_mode = 2
size_flags_horizontal = 3
text = "背包"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CloseButton" type="Button" parent="InventoryWindow/VBoxContainer/TitleBar"]
unique_name_in_owner = true
layout_mode = 2
text = "×"

[node name="HSeparator" type="HSeparator" parent="InventoryWindow/VBoxContainer"]
layout_mode = 2

[node name="MainContainer" type="HBoxContainer" parent="InventoryWindow/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="InventoryContainer" type="VBoxContainer" parent="InventoryWindow/VBoxContainer/MainContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="InventoryLabel" type="Label" parent="InventoryWindow/VBoxContainer/MainContainer/InventoryContainer"]
layout_mode = 2
text = "物品栏"
horizontal_alignment = 1

[node name="InventoryGrid" type="GridContainer" parent="InventoryWindow/VBoxContainer/MainContainer/InventoryContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3
columns = 6

[node name="VSeparator" type="VSeparator" parent="InventoryWindow/VBoxContainer/MainContainer"]
layout_mode = 2

[node name="RightPanel" type="VBoxContainer" parent="InventoryWindow/VBoxContainer/MainContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="EquipmentLabel" type="Label" parent="InventoryWindow/VBoxContainer/MainContainer/RightPanel"]
layout_mode = 2
text = "装备栏"
horizontal_alignment = 1

[node name="EquipmentContainer" type="VBoxContainer" parent="InventoryWindow/VBoxContainer/MainContainer/RightPanel"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3

[node name="WeaponSlot" type="Panel" parent="InventoryWindow/VBoxContainer/MainContainer/RightPanel/EquipmentContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="WeaponLabel" type="Label" parent="InventoryWindow/VBoxContainer/MainContainer/RightPanel/EquipmentContainer/WeaponSlot"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "武器"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ArmorSlot" type="Panel" parent="InventoryWindow/VBoxContainer/MainContainer/RightPanel/EquipmentContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ArmorLabel" type="Label" parent="InventoryWindow/VBoxContainer/MainContainer/RightPanel/EquipmentContainer/ArmorSlot"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "护甲"
horizontal_alignment = 1
vertical_alignment = 1

[node name="AccessorySlot" type="Panel" parent="InventoryWindow/VBoxContainer/MainContainer/RightPanel/EquipmentContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="AccessoryLabel" type="Label" parent="InventoryWindow/VBoxContainer/MainContainer/RightPanel/EquipmentContainer/AccessorySlot"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "饰品"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ShoesSlot" type="Panel" parent="InventoryWindow/VBoxContainer/MainContainer/RightPanel/EquipmentContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ShoesLabel" type="Label" parent="InventoryWindow/VBoxContainer/MainContainer/RightPanel/EquipmentContainer/ShoesSlot"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "鞋子"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HelmetSlot" type="Panel" parent="InventoryWindow/VBoxContainer/MainContainer/RightPanel/EquipmentContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="HelmetLabel" type="Label" parent="InventoryWindow/VBoxContainer/MainContainer/RightPanel/EquipmentContainer/HelmetSlot"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "头盔"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HSeparator2" type="HSeparator" parent="InventoryWindow/VBoxContainer"]
layout_mode = 2

[node name="InfoPanel" type="VBoxContainer" parent="InventoryWindow/VBoxContainer"]
layout_mode = 2

[node name="ItemInfo" type="VBoxContainer" parent="InventoryWindow/VBoxContainer/InfoPanel"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3

[node name="ItemName" type="Label" parent="InventoryWindow/VBoxContainer/InfoPanel/ItemInfo"]
unique_name_in_owner = true
layout_mode = 2
text = "选中物品信息"
horizontal_alignment = 1

[node name="ItemDescription" type="Label" parent="InventoryWindow/VBoxContainer/InfoPanel/ItemInfo"]
unique_name_in_owner = true
layout_mode = 2
text = "请选择一个物品查看详细信息"
autowrap_mode = 2

[node name="ActionButtons" type="HBoxContainer" parent="InventoryWindow/VBoxContainer/InfoPanel"]
unique_name_in_owner = true
layout_mode = 2

[node name="UseButton" type="Button" parent="InventoryWindow/VBoxContainer/InfoPanel/ActionButtons"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "使用"

[node name="DropButton" type="Button" parent="InventoryWindow/VBoxContainer/InfoPanel/ActionButtons"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "丢弃"