[gd_scene load_steps=14 format=3 uid="uid://dvtdgo0n3gkyq"]

[ext_resource type="Script" uid="uid://cnvyn0s45c3pg" path="res://Script/Player/test_runner.gd" id="1_hqrqu"]
[ext_resource type="Texture2D" uid="uid://dbcmxo51msi8m" path="res://Asset/Player/runner/shadow.png" id="2_ksaa3"]
[ext_resource type="Texture2D" uid="uid://bjippwgbxj0gi" path="res://Asset/Player/runner/circle_64x64.png" id="3_5y0ob"]
[ext_resource type="PackedScene" uid="uid://hjn4sr1lgmmx" path="res://Asset/Player/runner/runner_visual_purple.tscn" id="4_rd1xp"]
[ext_resource type="Shader" uid="uid://b5bdq1ogswoe2" path="res://Asset/Player/runner/character_contour.gdshader" id="4_wh7aa"]

[sub_resource type="Curve" id="Curve_a1o1w"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(0.8, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_157x8"]
curve = SubResource("Curve_a1o1w")

[sub_resource type="Curve" id="Curve_7ttaq"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_1ikpe"]
curve = SubResource("Curve_7ttaq")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_xw27s"]
particle_flag_disable_z = true
gravity = Vector3(0, -10, 0)
scale_min = 0.5
scale_max = 0.7
scale_curve = SubResource("CurveTexture_1ikpe")
alpha_curve = SubResource("CurveTexture_157x8")

[sub_resource type="CircleShape2D" id="CircleShape2D_5yhkr"]
radius = 31.0644

[sub_resource type="ShaderMaterial" id="ShaderMaterial_lp7tt"]
shader = ExtResource("4_wh7aa")
shader_parameter/viewport_scale = Vector2(1, 1)
shader_parameter/line_color = Color(0, 0, 0, 1)
shader_parameter/line_thickness = 4.0

[sub_resource type="ViewportTexture" id="ViewportTexture_70vh5"]
viewport_path = NodePath("SubViewport")

[node name="TestRunner" type="CharacterBody2D"]
motion_mode = 1
script = ExtResource("1_hqrqu")

[node name="Shadow" type="Sprite2D" parent="."]
modulate = Color(0, 0, 0, 0.156863)
position = Vector2(0, -12)
scale = Vector2(0.458333, 0.375)
texture = ExtResource("2_ksaa3")

[node name="Dust" type="GPUParticles2D" parent="."]
unique_name_in_owner = true
z_index = -1
position = Vector2(0, -16)
emitting = false
texture = ExtResource("3_5y0ob")
process_material = SubResource("ParticleProcessMaterial_xw27s")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(0, -36)
shape = SubResource("CircleShape2D_5yhkr")

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(0, -65)
limit_left = 0
limit_top = 0
limit_right = 3820
limit_bottom = 2690
limit_smoothed = true

[node name="SubViewport" type="SubViewport" parent="."]
transparent_bg = true
handle_input_locally = false
size = Vector2i(124, 138)
render_target_update_mode = 4

[node name="RunnerVisualPurple" parent="SubViewport" instance=ExtResource("4_rd1xp")]
unique_name_in_owner = true
position = Vector2(62, 120)
scale = Vector2(0.5, 0.5)

[node name="Sprite2D" type="Sprite2D" parent="."]
material = SubResource("ShaderMaterial_lp7tt")
position = Vector2(0, -65)
texture = SubResource("ViewportTexture_70vh5")
